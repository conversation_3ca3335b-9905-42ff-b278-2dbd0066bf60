import React, { useEffect } from 'react';
import {
  Table,
  Card,
  Button,
  Input,
  Select,
  Space,
  Tag,
  Typography,
  Tooltip,
  Modal,
  message,
  Progress,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  ReloadOutlined,
  ExportOutlined,
  EyeOutlined,
  EditOutlined,
  LockOutlined,
  UnlockOutlined,
} from '@ant-design/icons';
import { useAppDispatch, useAppSelector } from '@store/index';
import { fetchBaselines, freezeBaseline } from '@store/slices/baselineSlice';
import { setPageInfo } from '@store/slices/uiSlice';
import { Baseline } from '@types/index';

const { Text } = Typography;
const { Option } = Select;

const Baselines: React.FC = () => {
  const dispatch = useAppDispatch();
  const { baselines, total, loading } = useAppSelector(state => state.baselines);

  useEffect(() => {
    dispatch(setPageInfo({
      title: '基线管理',
      description: '管理KPI基线设置和冻结'
    }));
  }, [dispatch]);

  useEffect(() => {
    dispatch(fetchBaselines({ page: 1, size: 20 }));
  }, [dispatch]);

  const handleFreeze = (baseline: Baseline) => {
    Modal.confirm({
      title: '确认冻结基线',
      content: `确定要冻结基线"${baseline.name}"吗？冻结后将不能修改。`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          await dispatch(freezeBaseline(baseline.id)).unwrap();
          message.success('基线冻结成功');
        } catch (error: any) {
          message.error(error.message || '冻结失败');
        }
      },
    });
  };

  const getStatusColor = (status: string) => {
    const statusColors = {
      DRAFT: 'orange',
      ACTIVE: 'blue',
      FROZEN: 'green',
      EXPIRED: 'red',
    };
    return statusColors[status as keyof typeof statusColors] || 'default';
  };

  const getStatusText = (status: string) => {
    const statusTexts = {
      DRAFT: '草稿',
      ACTIVE: '生效中',
      FROZEN: '已冻结',
      EXPIRED: '已过期',
    };
    return statusTexts[status as keyof typeof statusTexts] || status;
  };

  const getMethodText = (method: string) => {
    const methodTexts = {
      AVERAGE: '平均值',
      MEDIAN: '中位数',
      WEIGHTED: '加权平均',
    };
    return methodTexts[method as keyof typeof methodTexts] || method;
  };

  const columns = [
    {
      title: '基线名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
    },
    {
      title: 'KPI名称',
      dataIndex: 'kpiName',
      key: 'kpiName',
      width: 150,
    },
    {
      title: '基线值',
      dataIndex: 'baselineValue',
      key: 'baselineValue',
      width: 100,
      render: (value: number) => (
        <Text strong>{value}%</Text>
      ),
    },
    {
      title: '计算方法',
      dataIndex: 'calculationMethod',
      key: 'calculationMethod',
      width: 100,
      render: (method: string) => (
        <Tag>{getMethodText(method)}</Tag>
      ),
    },
    {
      title: '版本',
      dataIndex: 'version',
      key: 'version',
      width: 80,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '冻结时间',
      dataIndex: 'frozenAt',
      key: 'frozenAt',
      width: 150,
      render: (date: string) => date ? date.substring(0, 16).replace('T', ' ') : '-',
    },
    {
      title: '冻结人',
      dataIndex: 'frozenBy',
      key: 'frozenBy',
      width: 100,
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, record: Baseline) => (
        <Space>
          <Tooltip title="查看详情">
            <Button type="text" icon={<EyeOutlined />} />
          </Tooltip>
          {record.status !== 'FROZEN' && (
            <>
              <Tooltip title="编辑">
                <Button type="text" icon={<EditOutlined />} />
              </Tooltip>
              <Tooltip title="冻结基线">
                <Button
                  type="text"
                  icon={<LockOutlined />}
                  onClick={() => handleFreeze(record)}
                />
              </Tooltip>
            </>
          )}
          {record.status === 'FROZEN' && (
            <Tooltip title="已冻结">
              <Button type="text" icon={<UnlockOutlined />} disabled />
            </Tooltip>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card>
        <div className="table-toolbar">
          <div className="table-toolbar-left">
            <Input.Search
              placeholder="搜索基线名称或KPI"
              allowClear
              style={{ width: 300 }}
              enterButton={<SearchOutlined />}
            />
            <Select
              placeholder="状态"
              allowClear
              style={{ width: 120 }}
            >
              <Option value="DRAFT">草稿</Option>
              <Option value="ACTIVE">生效中</Option>
              <Option value="FROZEN">已冻结</Option>
              <Option value="EXPIRED">已过期</Option>
            </Select>
          </div>
          <div className="table-toolbar-right">
            <Space>
              <Button icon={<ReloadOutlined />}>
                刷新
              </Button>
              <Button icon={<ExportOutlined />}>
                导出
              </Button>
              <Button type="primary" icon={<PlusOutlined />}>
                新建基线
              </Button>
            </Space>
          </div>
        </div>

        <Table
          columns={columns}
          dataSource={baselines}
          rowKey="id"
          loading={loading}
          pagination={{
            total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
          scroll={{ x: 1200 }}
        />
      </Card>
    </div>
  );
};

export default Baselines;
