import React, { useState, useEffect } from 'react';
import { 
  Form, 
  Input, 
  Button, 
  Card, 
  Typography, 
  Alert, 
  Divider, 
  Space, 
  Checkbox,
  Row,
  Col,
  Tooltip,
  Modal
} from 'antd';
import { 
  UserOutlined, 
  LockOutlined, 
  SafetyOutlined, 
  EyeInvisibleOutlined,
  EyeTwoTone,
  SecurityScanOutlined,
  QuestionCircleOutlined,
  GlobalOutlined
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '@hooks/useAuth';

const { Title, Text } = Typography;

interface LoginForm {
  username: string;
  password: string;
  tenantId?: string;
  rememberMe?: boolean;
}

const Login: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { login, isAuthenticated, loading, error } = useAuth();
  const [form] = Form.useForm();
  const [loginError, setLoginError] = useState<string | null>(null);
  const [showHelp, setShowHelp] = useState(false);
  const [showSecurityInfo, setShowSecurityInfo] = useState(false);

  // 如果已经登录，重定向到目标页面
  useEffect(() => {
    if (isAuthenticated) {
      const from = (location.state as any)?.from?.pathname || '/dashboard';
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, navigate, location]);

  const handleSubmit = async (values: LoginForm) => {
    try {
      setLoginError(null);
      await login(values);
    } catch (err: any) {
      setLoginError(err.message || '登录失败，请检查用户名和密码');
    }
  };

  // 演示账号登录
  const handleDemoLogin = (role: 'admin' | 'pm' | 'finance') => {
    const demoAccounts = {
      admin: { username: 'admin', password: 'admin123' },
      pm: { username: 'pm_zhang', password: 'pm123' },
      finance: { username: 'finance_li', password: 'finance123' },
    };

    form.setFieldsValue(demoAccounts[role]);
    handleSubmit(demoAccounts[role]);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* 顶部导航栏 */}
      <div className="absolute top-0 left-0 right-0 z-10">
        <div className="flex justify-between items-center p-6">
          <div className="flex items-center space-x-2">
            <GlobalOutlined className="text-blue-600 text-xl" />
            <Text className="text-slate-600 font-medium">多语言支持</Text>
          </div>
          <div className="flex items-center space-x-4">
            <Tooltip title="安全信息">
              <Button 
                type="text" 
                icon={<SecurityScanOutlined />} 
                onClick={() => setShowSecurityInfo(true)}
                className="text-slate-600"
              >
                安全中心
              </Button>
            </Tooltip>
            <Tooltip title="帮助中心">
              <Button 
                type="text" 
                icon={<QuestionCircleOutlined />} 
                onClick={() => setShowHelp(true)}
                className="text-slate-600"
              >
                帮助
              </Button>
            </Tooltip>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex min-h-screen">
        {/* 左侧品牌展示区 */}
        <div className="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-blue-600 to-indigo-700 relative overflow-hidden">
          <div className="absolute inset-0 bg-black bg-opacity-10"></div>
          <div className="relative z-10 flex flex-col justify-center items-center text-white p-12">
            <div className="mb-8">
              <div className="h-24 w-24 bg-white bg-opacity-20 rounded-2xl flex items-center justify-center text-white text-4xl font-bold backdrop-blur-sm">
                PC
              </div>
            </div>
            <Title level={1} className="text-white mb-4 text-center">
              项目协同管理系统
            </Title>
            <Text className="text-blue-100 text-lg text-center mb-8 max-w-md">
              基于结果导向的企业级项目管理平台，助力团队高效协作，实现项目价值最大化
            </Text>
            <div className="grid grid-cols-2 gap-6 text-center">
              <div className="bg-white bg-opacity-10 rounded-lg p-4 backdrop-blur-sm">
                <div className="text-2xl font-bold mb-1">99.9%</div>
                <div className="text-blue-100 text-sm">系统可用性</div>
              </div>
              <div className="bg-white bg-opacity-10 rounded-lg p-4 backdrop-blur-sm">
                <div className="text-2xl font-bold mb-1">24/7</div>
                <div className="text-blue-100 text-sm">技术支持</div>
              </div>
            </div>
          </div>
          {/* 装饰性几何图形 */}
          <div className="absolute top-20 right-20 w-32 h-32 border border-white border-opacity-20 rounded-full"></div>
          <div className="absolute bottom-20 left-20 w-24 h-24 border border-white border-opacity-20 rounded-lg rotate-45"></div>
        </div>

        {/* 右侧登录表单区 */}
        <div className="flex-1 flex items-center justify-center p-8">
          <div className="w-full max-w-md">
            {/* 移动端品牌展示 */}
            <div className="lg:hidden text-center mb-8">
              <div className="mb-4">
                <div className="h-16 w-16 mx-auto bg-blue-600 rounded-xl flex items-center justify-center text-white text-2xl font-bold">
                  PC
                </div>
              </div>
              <Title level={2} className="mb-2 text-slate-800">
                项目协同管理系统
              </Title>
              <Text type="secondary">
                基于结果导向的项目管理平台
              </Text>
            </div>

            <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
              <div className="mb-6">
                <Title level={3} className="text-center mb-2 text-slate-800">
                  欢迎登录
                </Title>
                <Text type="secondary" className="block text-center">
                  请输入您的账户信息以继续
                </Text>
              </div>

              {(error || loginError) && (
                <Alert
                  message={error || loginError}
                  type="error"
                  showIcon
                  className="mb-6"
                  closable
                  onClose={() => setLoginError(null)}
                />
              )}

              <Form
                form={form}
                name="login"
                onFinish={handleSubmit}
                autoComplete="off"
                size="large"
                layout="vertical"
              >
                <Form.Item
                  label="用户名"
                  name="username"
                  rules={[
                    { required: true, message: '请输入用户名' },
                    { min: 2, message: '用户名至少2个字符' },
                    { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线' }
                  ]}
                >
                  <Input
                    prefix={<UserOutlined className="text-slate-400" />}
                    placeholder="请输入用户名"
                    autoComplete="username"
                    className="h-12"
                  />
                </Form.Item>

                <Form.Item
                  label="密码"
                  name="password"
                  rules={[
                    { required: true, message: '请输入密码' },
                    { min: 6, message: '密码至少6个字符' },
                  ]}
                >
                  <Input.Password
                    prefix={<LockOutlined className="text-slate-400" />}
                    placeholder="请输入密码"
                    autoComplete="current-password"
                    className="h-12"
                    iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
                  />
                </Form.Item>

                <Form.Item
                  label={(
                    <span>
                      租户ID 
                      <Tooltip title="多租户环境下需要指定租户ID，如果您不确定，请联系系统管理员">
                        <QuestionCircleOutlined className="ml-1 text-slate-400" />
                      </Tooltip>
                    </span>
                  )}
                  name="tenantId"
                >
                  <Input
                    prefix={<SafetyOutlined className="text-slate-400" />}
                    placeholder="租户ID (可选)"
                    autoComplete="organization"
                    className="h-12"
                  />
                </Form.Item>

                <Row justify="space-between" align="middle" className="mb-6">
                  <Col>
                    <Form.Item name="rememberMe" valuePropName="checked" noStyle>
                      <Checkbox className="text-slate-600">
                        记住我
                      </Checkbox>
                    </Form.Item>
                  </Col>
                  <Col>
                    <Button type="link" className="p-0 text-blue-600 hover:text-blue-700">
                      忘记密码？
                    </Button>
                  </Col>
                </Row>

                <Form.Item>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={loading}
                    block
                    className="h-12 text-base font-medium bg-blue-600 hover:bg-blue-700 border-blue-600 hover:border-blue-700"
                  >
                    {loading ? '登录中...' : '立即登录'}
                  </Button>
                </Form.Item>
              </Form>

              <Divider className="my-6">
                <Text type="secondary" className="text-sm">演示账号快速登录</Text>
              </Divider>

              <div className="space-y-3">
                <Row gutter={8}>
                  <Col span={8}>
                    <Button
                      size="small"
                      block
                      onClick={() => handleDemoLogin('admin')}
                      disabled={loading}
                      className="text-xs"
                    >
                      管理员
                    </Button>
                  </Col>
                  <Col span={8}>
                    <Button
                      size="small"
                      block
                      onClick={() => handleDemoLogin('pm')}
                      disabled={loading}
                      className="text-xs"
                    >
                      项目经理
                    </Button>
                  </Col>
                  <Col span={8}>
                    <Button
                      size="small"
                      block
                      onClick={() => handleDemoLogin('finance')}
                      disabled={loading}
                      className="text-xs"
                    >
                      财务总监
                    </Button>
                  </Col>
                </Row>
              </div>

              {/* 安全提示 */}
              <div className="mt-6 p-3 bg-blue-50 rounded-lg border border-blue-200">
                <div className="flex items-start space-x-2">
                  <SecurityScanOutlined className="text-blue-600 mt-0.5" />
                  <div>
                    <Text className="text-blue-800 text-sm font-medium block">安全提示</Text>
                    <Text className="text-blue-600 text-xs">
                      为保护您的账户安全，请勿在公共设备上保存登录信息
                    </Text>
                  </div>
                </div>
              </div>

              {/* 底部信息 */}
              <div className="text-center mt-6 space-y-2">
                <div className="flex justify-center space-x-4 text-xs">
                  <Button type="link" size="small" className="p-0 h-auto text-slate-500">
                    服务条款
                  </Button>
                  <Button type="link" size="small" className="p-0 h-auto text-slate-500">
                    隐私政策
                  </Button>
                  <Button type="link" size="small" className="p-0 h-auto text-slate-500">
                    技术支持
                  </Button>
                </div>
                <Text type="secondary" className="text-xs block">
                  © 2025 项目协同管理系统. All rights reserved.
                </Text>
                <Text type="secondary" className="text-xs block">
                  Version 1.0.0 | 建议使用Chrome、Firefox或Edge浏览器
                </Text>
              </div>
            </Card>
          </div>
        </div>
      </div>

      {/* 帮助模态框 */}
      <Modal
        title="帮助中心"
        open={showHelp}
        onCancel={() => setShowHelp(false)}
        footer={[
          <Button key="close" onClick={() => setShowHelp(false)}>
            关闭
          </Button>
        ]}
        width={600}
      >
        <div className="space-y-4">
          <div>
            <Title level={5}>登录问题</Title>
            <ul className="list-disc list-inside space-y-1 text-sm text-slate-600">
              <li>忘记密码：请联系系统管理员重置密码</li>
              <li>账户被锁定：连续输错密码5次后账户将被锁定30分钟</li>
              <li>租户ID：多租户环境下必须输入正确的租户ID</li>
            </ul>
          </div>
          <div>
            <Title level={5}>技术支持</Title>
            <ul className="list-disc list-inside space-y-1 text-sm text-slate-600">
              <li>技术支持热线：400-123-4567</li>
              <li>邮箱支持：<EMAIL></li>
              <li>在线客服：工作日 9:00-18:00</li>
            </ul>
          </div>
          <div>
            <Title level={5}>系统要求</Title>
            <ul className="list-disc list-inside space-y-1 text-sm text-slate-600">
              <li>推荐浏览器：Chrome 90+、Firefox 88+、Edge 90+</li>
              <li>屏幕分辨率：1366x768 或更高</li>
              <li>网络要求：稳定的互联网连接</li>
            </ul>
          </div>
        </div>
      </Modal>

      {/* 安全信息模态框 */}
      <Modal
        title="安全中心"
        open={showSecurityInfo}
        onCancel={() => setShowSecurityInfo(false)}
        footer={[
          <Button key="close" onClick={() => setShowSecurityInfo(false)}>
            关闭
          </Button>
        ]}
        width={600}
      >
        <div className="space-y-4">
          <div>
            <Title level={5}>数据安全</Title>
            <ul className="list-disc list-inside space-y-1 text-sm text-slate-600">
              <li>所有数据传输均采用SSL/TLS加密</li>
              <li>密码采用bcrypt算法加密存储</li>
              <li>系统通过ISO 27001信息安全认证</li>
            </ul>
          </div>
          <div>
            <Title level={5}>访问控制</Title>
            <ul className="list-disc list-inside space-y-1 text-sm text-slate-600">
              <li>基于角色的权限控制(RBAC)</li>
              <li>支持多因素身份验证(MFA)</li>
              <li>会话超时自动登出保护</li>
            </ul>
          </div>
          <div>
            <Title level={5}>合规认证</Title>
            <ul className="list-disc list-inside space-y-1 text-sm text-slate-600">
              <li>符合GDPR数据保护法规</li>
              <li>通过SOC 2 Type II审计</li>
              <li>定期进行安全渗透测试</li>
            </ul>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default Login;
