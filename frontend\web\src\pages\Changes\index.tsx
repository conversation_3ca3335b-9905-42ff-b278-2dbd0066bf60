import React, { useEffect, useState } from 'react';
import {
  Table,
  Card,
  Button,
  Input,
  Select,
  Space,
  Tag,
  Typography,
  Tooltip,
  Modal,
  message,
  Descriptions,
  Drawer,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  ReloadOutlined,
  ExportOutlined,
  EyeOutlined,
  EditOutlined,
  CheckOutlined,
  CloseOutlined,
  FileTextOutlined,
} from '@ant-design/icons';
import { useAppDispatch, useAppSelector } from '@store/index';
import { fetchChangeRequests, approveChangeRequest, rejectChangeRequest } from '@store/slices/changeSlice';
import { setPageInfo } from '@store/slices/uiSlice';
import { ChangeRequest } from '@types/index';

const { Text } = Typography;
const { Option } = Select;

const Changes: React.FC = () => {
  const dispatch = useAppDispatch();
  const { changes, total, loading } = useAppSelector(state => state.changes);
  const [selectedChange, setSelectedChange] = useState<ChangeRequest | null>(null);
  const [drawerVisible, setDrawerVisible] = useState(false);

  useEffect(() => {
    dispatch(setPageInfo({
      title: '变更管理',
      description: '管理项目变更请求和审批流程'
    }));
  }, [dispatch]);

  useEffect(() => {
    dispatch(fetchChangeRequests({ page: 1, size: 20 }));
  }, [dispatch]);

  const handleApprove = (change: ChangeRequest) => {
    Modal.confirm({
      title: '确认批准',
      content: `确定要批准变更请求"${change.title}"吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          await dispatch(approveChangeRequest({ id: change.id })).unwrap();
          message.success('变更请求已批准');
        } catch (error: any) {
          message.error(error.message || '批准失败');
        }
      },
    });
  };

  const handleReject = (change: ChangeRequest) => {
    Modal.confirm({
      title: '确认拒绝',
      content: (
        <div>
          <p>确定要拒绝变更请求"{change.title}"吗？</p>
          <Input.TextArea
            placeholder="请输入拒绝原因"
            rows={3}
            id="rejectReason"
          />
        </div>
      ),
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        const reason = (document.getElementById('rejectReason') as HTMLTextAreaElement)?.value;
        if (!reason) {
          message.error('请输入拒绝原因');
          return;
        }
        try {
          await dispatch(rejectChangeRequest({ id: change.id, comment: reason })).unwrap();
          message.success('变更请求已拒绝');
        } catch (error: any) {
          message.error(error.message || '拒绝失败');
        }
      },
    });
  };

  const handleViewDetail = (change: ChangeRequest) => {
    setSelectedChange(change);
    setDrawerVisible(true);
  };

  const getStatusColor = (status: string) => {
    const statusColors = {
      DRAFT: 'default',
      SUBMITTED: 'blue',
      EVALUATING: 'orange',
      CUSTOMER_REVIEW: 'purple',
      APPROVED: 'green',
      REJECTED: 'red',
      EXECUTING: 'cyan',
      COMPLETED: 'success',
      CANCELLED: 'error',
    };
    return statusColors[status as keyof typeof statusColors] || 'default';
  };

  const getStatusText = (status: string) => {
    const statusTexts = {
      DRAFT: '草稿',
      SUBMITTED: '已提交',
      EVALUATING: '评估中',
      CUSTOMER_REVIEW: '客户审核',
      APPROVED: '已批准',
      REJECTED: '已拒绝',
      EXECUTING: '执行中',
      COMPLETED: '已完成',
      CANCELLED: '已取消',
    };
    return statusTexts[status as keyof typeof statusTexts] || status;
  };

  const getTypeColor = (type: string) => {
    const typeColors = {
      REQUIREMENT: 'blue',
      DESIGN: 'green',
      TECHNICAL: 'orange',
      COMMERCIAL: 'purple',
    };
    return typeColors[type as keyof typeof typeColors] || 'default';
  };

  const getTypeText = (type: string) => {
    const typeTexts = {
      REQUIREMENT: '需求变更',
      DESIGN: '设计变更',
      TECHNICAL: '技术变更',
      COMMERCIAL: '商务变更',
    };
    return typeTexts[type as keyof typeof typeTexts] || type;
  };

  const getSourceText = (source: string) => {
    const sourceTexts = {
      CUSTOMER: '客户',
      INTERNAL: '内部',
      SUPPLIER: '供应商',
    };
    return sourceTexts[source as keyof typeof sourceTexts] || source;
  };

  const columns = [
    {
      title: '变更编号',
      dataIndex: 'changeNo',
      key: 'changeNo',
      width: 120,
    },
    {
      title: '变更标题',
      dataIndex: 'title',
      key: 'title',
      width: 200,
      render: (text: string, record: ChangeRequest) => (
        <Button
          type="link"
          onClick={() => handleViewDetail(record)}
          className="p-0 h-auto text-left"
        >
          {text}
        </Button>
      ),
    },
    {
      title: '项目名称',
      dataIndex: 'projectName',
      key: 'projectName',
      width: 150,
    },
    {
      title: '变更类型',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      render: (type: string) => (
        <Tag color={getTypeColor(type)}>
          {getTypeText(type)}
        </Tag>
      ),
    },
    {
      title: '变更来源',
      dataIndex: 'source',
      key: 'source',
      width: 80,
      render: (source: string) => getSourceText(source),
    },
    {
      title: '成本影响',
      dataIndex: 'impactCost',
      key: 'impactCost',
      width: 100,
      render: (cost: number) => (
        <Text type={cost > 0 ? 'danger' : 'secondary'}>
          {cost ? `¥${cost.toLocaleString()}` : '-'}
        </Text>
      ),
    },
    {
      title: '工期影响',
      dataIndex: 'impactDays',
      key: 'impactDays',
      width: 80,
      render: (days: number) => (
        <Text type={days > 0 ? 'warning' : 'secondary'}>
          {days ? `${days}天` : '-'}
        </Text>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '提交时间',
      dataIndex: 'submittedAt',
      key: 'submittedAt',
      width: 150,
      render: (date: string) => date ? date.substring(0, 16).replace('T', ' ') : '-',
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, record: ChangeRequest) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record)}
            />
          </Tooltip>
          {record.status === 'SUBMITTED' && (
            <>
              <Tooltip title="批准">
                <Button
                  type="text"
                  icon={<CheckOutlined />}
                  onClick={() => handleApprove(record)}
                />
              </Tooltip>
              <Tooltip title="拒绝">
                <Button
                  type="text"
                  icon={<CloseOutlined />}
                  onClick={() => handleReject(record)}
                />
              </Tooltip>
            </>
          )}
          {record.status === 'DRAFT' && (
            <Tooltip title="编辑">
              <Button type="text" icon={<EditOutlined />} />
            </Tooltip>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card>
        <div className="table-toolbar">
          <div className="table-toolbar-left">
            <Input.Search
              placeholder="搜索变更标题或编号"
              allowClear
              style={{ width: 300 }}
              enterButton={<SearchOutlined />}
            />
            <Select
              placeholder="变更状态"
              allowClear
              style={{ width: 120 }}
            >
              <Option value="DRAFT">草稿</Option>
              <Option value="SUBMITTED">已提交</Option>
              <Option value="EVALUATING">评估中</Option>
              <Option value="CUSTOMER_REVIEW">客户审核</Option>
              <Option value="APPROVED">已批准</Option>
              <Option value="REJECTED">已拒绝</Option>
            </Select>
            <Select
              placeholder="变更类型"
              allowClear
              style={{ width: 120 }}
            >
              <Option value="REQUIREMENT">需求变更</Option>
              <Option value="DESIGN">设计变更</Option>
              <Option value="TECHNICAL">技术变更</Option>
              <Option value="COMMERCIAL">商务变更</Option>
            </Select>
          </div>
          <div className="table-toolbar-right">
            <Space>
              <Button icon={<ReloadOutlined />}>
                刷新
              </Button>
              <Button icon={<ExportOutlined />}>
                导出
              </Button>
              <Button type="primary" icon={<PlusOutlined />}>
                新建变更
              </Button>
            </Space>
          </div>
        </div>

        <Table
          columns={columns}
          dataSource={changes}
          rowKey="id"
          loading={loading}
          pagination={{
            total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
          scroll={{ x: 1400 }}
        />
      </Card>

      {/* 变更详情抽屉 */}
      <Drawer
        title="变更详情"
        placement="right"
        width={600}
        open={drawerVisible}
        onClose={() => setDrawerVisible(false)}
      >
        {selectedChange && (
          <div>
            <Descriptions column={1} bordered>
              <Descriptions.Item label="变更编号">
                {selectedChange.changeNo}
              </Descriptions.Item>
              <Descriptions.Item label="变更标题">
                {selectedChange.title}
              </Descriptions.Item>
              <Descriptions.Item label="项目名称">
                {selectedChange.projectName}
              </Descriptions.Item>
              <Descriptions.Item label="变更类型">
                <Tag color={getTypeColor(selectedChange.type)}>
                  {getTypeText(selectedChange.type)}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="变更来源">
                {getSourceText(selectedChange.source)}
              </Descriptions.Item>
              <Descriptions.Item label="成本影响">
                <Text type={selectedChange.impactCost && selectedChange.impactCost > 0 ? 'danger' : 'secondary'}>
                  {selectedChange.impactCost ? `¥${selectedChange.impactCost.toLocaleString()}` : '无影响'}
                </Text>
              </Descriptions.Item>
              <Descriptions.Item label="工期影响">
                <Text type={selectedChange.impactDays && selectedChange.impactDays > 0 ? 'warning' : 'secondary'}>
                  {selectedChange.impactDays ? `${selectedChange.impactDays}天` : '无影响'}
                </Text>
              </Descriptions.Item>
              <Descriptions.Item label="质量影响">
                {selectedChange.impactQuality || '无影响'}
              </Descriptions.Item>
              <Descriptions.Item label="当前状态">
                <Tag color={getStatusColor(selectedChange.status)}>
                  {getStatusText(selectedChange.status)}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="变更描述">
                {selectedChange.description}
              </Descriptions.Item>
            </Descriptions>

            {selectedChange.attachments && selectedChange.attachments.length > 0 && (
              <div className="mt-4">
                <Text strong>附件文件：</Text>
                <div className="mt-2">
                  {selectedChange.attachments.map((file, index) => (
                    <div key={index} className="flex items-center mb-2">
                      <FileTextOutlined className="mr-2" />
                      <Text>{file.fileName}</Text>
                      <Text type="secondary" className="ml-2">
                        ({(file.fileSize / 1024).toFixed(1)}KB)
                      </Text>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {selectedChange.approvalHistory && selectedChange.approvalHistory.length > 0 && (
              <div className="mt-4">
                <Text strong>审批历史：</Text>
                <div className="mt-2">
                  {selectedChange.approvalHistory.map((approval, index) => (
                    <div key={index} className="mb-2 p-2 bg-gray-50 rounded">
                      <div className="flex justify-between">
                        <Text strong>{approval.approverName}</Text>
                        <Text type="secondary">
                          {approval.approvedAt.substring(0, 16).replace('T', ' ')}
                        </Text>
                      </div>
                      <div className="mt-1">
                        <Tag color={approval.action === 'APPROVE' ? 'green' : 'red'}>
                          {approval.action === 'APPROVE' ? '批准' : '拒绝'}
                        </Tag>
                        {approval.comment && (
                          <Text className="ml-2">{approval.comment}</Text>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </Drawer>
    </div>
  );
};

export default Changes;
