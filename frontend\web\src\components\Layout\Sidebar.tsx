import React from 'react';
import { Layout, Menu } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  DashboardOutlined,
  ProjectOutlined,
  UserOutlined,
  FileTextOutlined,
  BarChartOutlined,
  SettingOutlined,
  SwapOutlined,
  DollarOutlined,
  CheckCircleOutlined,
  WarningOutlined,
  FileSearchOutlined,
  TeamOutlined,
} from '@ant-design/icons';
import { usePermission } from '@hooks/useAuth';
import { MenuItem } from '@types/index';

const { Sider } = Layout;

interface SidebarProps {
  collapsed: boolean;
}

const Sidebar: React.FC<SidebarProps> = ({ collapsed }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { hasPermission, hasRole } = usePermission();

  // 菜单配置
  const menuItems: MenuItem[] = [
    {
      key: 'dashboard',
      label: '仪表盘',
      icon: 'DashboardOutlined',
      path: '/dashboard',
    },
    {
      key: 'projects',
      label: '项目管理',
      icon: 'ProjectOutlined',
      children: [
        {
          key: 'projects-list',
          label: '项目列表',
          path: '/projects',
        },
        {
          key: 'milestones',
          label: '里程碑管理',
          path: '/projects/milestones',
        },
      ],
    },
    {
      key: 'customers',
      label: '客户管理',
      icon: 'TeamOutlined',
      path: '/customers',
    },
    {
      key: 'contracts',
      label: '合同管理',
      icon: 'FileTextOutlined',
      path: '/contracts',
    },
    {
      key: 'kpi',
      label: 'KPI管理',
      icon: 'BarChartOutlined',
      children: [
        {
          key: 'kpi-definitions',
          label: 'KPI定义',
          path: '/kpi',
        },
        {
          key: 'kpi-facts',
          label: 'KPI数据',
          path: '/kpi/facts',
        },
      ],
    },
    {
      key: 'baselines',
      label: '基线管理',
      icon: 'CheckCircleOutlined',
      path: '/baselines',
    },
    {
      key: 'changes',
      label: '变更管理',
      icon: 'SwapOutlined',
      path: '/changes',
    },
    {
      key: 'settlements',
      label: '结算管理',
      icon: 'DollarOutlined',
      path: '/settlements',
    },
    {
      key: 'data-health',
      label: '数据健康',
      icon: 'WarningOutlined',
      path: '/data-health',
    },
    {
      key: 'reports',
      label: '报表中心',
      icon: 'FileSearchOutlined',
      path: '/reports',
    },
    {
      key: 'settings',
      label: '系统设置',
      icon: 'SettingOutlined',
      children: [
        {
          key: 'settings-users',
          label: '用户管理',
          path: '/settings/users',
        },
        {
          key: 'settings-roles',
          label: '角色权限',
          path: '/settings/roles',
        },
        {
          key: 'settings-system',
          label: '系统配置',
          path: '/settings/system',
        },
      ],
    },
  ];

  // 图标映射
  const iconMap: Record<string, React.ReactNode> = {
    DashboardOutlined: <DashboardOutlined />,
    ProjectOutlined: <ProjectOutlined />,
    UserOutlined: <UserOutlined />,
    FileTextOutlined: <FileTextOutlined />,
    BarChartOutlined: <BarChartOutlined />,
    SettingOutlined: <SettingOutlined />,
    SwapOutlined: <SwapOutlined />,
    DollarOutlined: <DollarOutlined />,
    CheckCircleOutlined: <CheckCircleOutlined />,
    WarningOutlined: <WarningOutlined />,
    FileSearchOutlined: <FileSearchOutlined />,
    TeamOutlined: <TeamOutlined />,
  };

  // 过滤菜单项（基于权限）
  const filterMenuItems = (items: MenuItem[]): MenuItem[] => {
    return items.filter(item => {
      // 如果有权限要求，检查权限
      if (item.permission && !hasPermission(item.permission)) {
        return false;
      }

      // 递归过滤子菜单
      if (item.children) {
        item.children = filterMenuItems(item.children);
        // 如果所有子菜单都被过滤掉，则隐藏父菜单
        return item.children.length > 0;
      }

      return true;
    });
  };

  // 转换为Ant Design Menu格式
  const convertToAntdMenuItems = (items: MenuItem[]): any[] => {
    return items.map(item => ({
      key: item.key,
      icon: item.icon ? iconMap[item.icon] : null,
      label: item.label,
      children: item.children ? convertToAntdMenuItems(item.children) : undefined,
      onClick: item.path ? () => navigate(item.path!) : undefined,
    }));
  };

  // 获取当前选中的菜单项
  const getSelectedKeys = (): string[] => {
    const path = location.pathname;
    
    // 精确匹配
    for (const item of menuItems) {
      if (item.path === path) {
        return [item.key];
      }
      
      if (item.children) {
        for (const child of item.children) {
          if (child.path === path) {
            return [child.key];
          }
        }
      }
    }

    // 模糊匹配
    if (path.startsWith('/projects')) return ['projects-list'];
    if (path.startsWith('/customers')) return ['customers'];
    if (path.startsWith('/contracts')) return ['contracts'];
    if (path.startsWith('/kpi')) return ['kpi-definitions'];
    if (path.startsWith('/baselines')) return ['baselines'];
    if (path.startsWith('/changes')) return ['changes'];
    if (path.startsWith('/settlements')) return ['settlements'];
    if (path.startsWith('/data-health')) return ['data-health'];
    if (path.startsWith('/reports')) return ['reports'];
    if (path.startsWith('/settings')) return ['settings-users'];
    
    return ['dashboard'];
  };

  // 获取展开的菜单项
  const getOpenKeys = (): string[] => {
    const path = location.pathname;
    const openKeys: string[] = [];

    if (path.startsWith('/projects')) openKeys.push('projects');
    if (path.startsWith('/kpi')) openKeys.push('kpi');
    if (path.startsWith('/settings')) openKeys.push('settings');

    return openKeys;
  };

  const filteredMenuItems = filterMenuItems(menuItems);
  const antdMenuItems = convertToAntdMenuItems(filteredMenuItems);

  return (
    <Sider
      trigger={null}
      collapsible
      collapsed={collapsed}
      className="app-sider"
      width={256}
      collapsedWidth={80}
    >
      <div className="h-16 flex items-center justify-center border-b border-gray-700">
        {!collapsed ? (
          <div className="text-white font-bold text-lg">
            项目协同
          </div>
        ) : (
          <div className="text-white font-bold text-xl">
            PC
          </div>
        )}
      </div>
      
      <Menu
        theme="dark"
        mode="inline"
        selectedKeys={getSelectedKeys()}
        defaultOpenKeys={getOpenKeys()}
        items={antdMenuItems}
        className="border-r-0"
      />
    </Sider>
  );
};

export default Sidebar;
