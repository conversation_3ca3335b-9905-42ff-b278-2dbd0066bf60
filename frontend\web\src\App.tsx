import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider, App as AntdApp } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { Provider } from 'react-redux';
import { store } from '@store/index';
import { AuthProvider } from '@hooks/useAuth';
import Layout from '@components/Layout';
import Login from '@pages/Login';
import Dashboard from '@pages/Dashboard';
import Projects from '@pages/Projects';
import ProjectDetail from '@pages/Projects/ProjectDetail';
import Customers from '@pages/Customers';
import Contracts from '@pages/Contracts';
import KpiManagement from '@pages/KpiManagement';
import KpiFacts from '@pages/KpiManagement/KpiFacts';
import Baselines from '@pages/Baselines';
import Changes from '@pages/Changes';
import Settlements from '@pages/Settlements';
import DataHealth from '@pages/DataHealth';
import Reports from '@pages/Reports';
import Settings from '@pages/Settings';
import NotFound from '@pages/NotFound';
import ProtectedRoute from '@components/ProtectedRoute';
import './App.css';

// Ant Design 主题配置
const theme = {
  token: {
    colorPrimary: '#1890ff',
    colorSuccess: '#52c41a',
    colorWarning: '#faad14',
    colorError: '#ff4d4f',
    colorInfo: '#1890ff',
    borderRadius: 6,
    wireframe: false,
  },
  components: {
    Layout: {
      headerBg: '#fff',
      siderBg: '#001529',
    },
    Menu: {
      darkItemBg: '#001529',
      darkSubMenuItemBg: '#000c17',
      darkItemSelectedBg: '#1890ff',
    },
  },
};

const App: React.FC = () => {
  return (
    <Provider store={store}>
      <ConfigProvider locale={zhCN} theme={theme}>
        <AntdApp>
          <AuthProvider>
            <Router>
              <Routes>
                {/* 登录页面 */}
                <Route path="/login" element={<Login />} />
                
                {/* 主应用路由 */}
                <Route path="/" element={
                  <ProtectedRoute>
                    <Layout />
                  </ProtectedRoute>
                }>
                  {/* 仪表盘 */}
                  <Route index element={<Navigate to="/dashboard" replace />} />
                  <Route path="dashboard" element={<Dashboard />} />
                  
                  {/* 项目管理 */}
                  <Route path="projects" element={<Projects />} />
                  <Route path="projects/:id" element={<ProjectDetail />} />
                  
                  {/* 客户管理 */}
                  <Route path="customers" element={<Customers />} />
                  
                  {/* 合同管理 */}
                  <Route path="contracts" element={<Contracts />} />
                  
                  {/* KPI管理 */}
                  <Route path="kpi" element={<KpiManagement />} />
                  <Route path="kpi/facts" element={<KpiFacts />} />
                  
                  {/* 基线管理 */}
                  <Route path="baselines" element={<Baselines />} />
                  
                  {/* 变更管理 */}
                  <Route path="changes" element={<Changes />} />
                  
                  {/* 结算管理 */}
                  <Route path="settlements" element={<Settlements />} />
                  
                  {/* 数据健康 */}
                  <Route path="data-health" element={<DataHealth />} />
                  
                  {/* 报表中心 */}
                  <Route path="reports" element={<Reports />} />
                  
                  {/* 系统设置 */}
                  <Route path="settings" element={<Settings />} />
                </Route>
                
                {/* 404页面 */}
                <Route path="*" element={<NotFound />} />
              </Routes>
            </Router>
          </AuthProvider>
        </AntdApp>
      </ConfigProvider>
    </Provider>
  );
};

export default App;
