import React, { useEffect, useState } from 'react';
import {
  Card,
  Typography,
  Tabs,
  Form,
  Input,
  Switch,
  Button,
  Select,
  InputNumber,
  Table,
  Space,
  Tag,
  Modal,
  message,
  Tooltip,
  Divider,
  Alert,
  Upload,
  Avatar,
} from 'antd';
import {
  UserOutlined,
  SettingOutlined,
  SecurityScanOutlined,
  DatabaseOutlined,
  BellOutlined,
  UploadOutlined,
  EditOutlined,
  DeleteOutlined,
  PlusOutlined,
  SaveOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import { useAppDispatch } from '@store/index';
import { setPageInfo } from '@store/slices/uiSlice';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;

interface User {
  id: string;
  username: string;
  fullName: string;
  email: string;
  roles: string[];
  status: 'ACTIVE' | 'INACTIVE';
  lastLogin: string;
  createdAt: string;
}

interface Role {
  id: string;
  code: string;
  name: string;
  description: string;
  permissions: string[];
  userCount: number;
}

const Settings: React.FC = () => {
  const dispatch = useAppDispatch();
  const [form] = Form.useForm();
  const [userForm] = Form.useForm();
  const [roleForm] = Form.useForm();
  const [userModalVisible, setUserModalVisible] = useState(false);
  const [roleModalVisible, setRoleModalVisible] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [editingRole, setEditingRole] = useState<Role | null>(null);

  useEffect(() => {
    dispatch(setPageInfo({
      title: '系统设置',
      description: '管理系统配置和用户权限'
    }));
  }, [dispatch]);

  // 模拟用户数据
  const users: User[] = [
    {
      id: 'user_001',
      username: 'admin',
      fullName: '系统管理员',
      email: '<EMAIL>',
      roles: ['ADMIN'],
      status: 'ACTIVE',
      lastLogin: '2025-01-14 10:30:00',
      createdAt: '2024-01-01 00:00:00',
    },
    {
      id: 'user_002',
      username: 'pm_zhang',
      fullName: '张项目经理',
      email: '<EMAIL>',
      roles: ['PROJECT_MANAGER'],
      status: 'ACTIVE',
      lastLogin: '2025-01-14 09:15:00',
      createdAt: '2024-02-15 00:00:00',
    },
    {
      id: 'user_003',
      username: 'finance_li',
      fullName: '李财务总监',
      email: '<EMAIL>',
      roles: ['FINANCE_MANAGER'],
      status: 'ACTIVE',
      lastLogin: '2025-01-13 16:45:00',
      createdAt: '2024-01-10 00:00:00',
    },
  ];

  // 模拟角色数据
  const roles: Role[] = [
    {
      id: 'role_001',
      code: 'ADMIN',
      name: '系统管理员',
      description: '拥有系统所有权限',
      permissions: ['*'],
      userCount: 1,
    },
    {
      id: 'role_002',
      code: 'PROJECT_MANAGER',
      name: '项目经理',
      description: '项目管理相关权限',
      permissions: ['project:read', 'project:write', 'kpi:read'],
      userCount: 5,
    },
    {
      id: 'role_003',
      code: 'FINANCE_MANAGER',
      name: '财务经理',
      description: '财务管理相关权限',
      permissions: ['settlement:read', 'settlement:write', 'financial:read'],
      userCount: 2,
    },
  ];

  const handleSaveSystemConfig = async (values: any) => {
    try {
      console.log('保存系统配置:', values);
      message.success('系统配置保存成功');
    } catch (error) {
      message.error('保存失败');
    }
  };

  const handleCreateUser = () => {
    setEditingUser(null);
    userForm.resetFields();
    setUserModalVisible(true);
  };

  const handleEditUser = (user: User) => {
    setEditingUser(user);
    userForm.setFieldsValue(user);
    setUserModalVisible(true);
  };

  const handleDeleteUser = (user: User) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除用户"${user.fullName}"吗？`,
      onOk: () => {
        message.success('用户删除成功');
      },
    });
  };

  const handleSaveUser = async () => {
    try {
      const values = await userForm.validateFields();
      console.log('保存用户:', values);
      message.success(editingUser ? '用户更新成功' : '用户创建成功');
      setUserModalVisible(false);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const handleCreateRole = () => {
    setEditingRole(null);
    roleForm.resetFields();
    setRoleModalVisible(true);
  };

  const handleEditRole = (role: Role) => {
    setEditingRole(role);
    roleForm.setFieldsValue(role);
    setRoleModalVisible(true);
  };

  const handleSaveRole = async () => {
    try {
      const values = await roleForm.validateFields();
      console.log('保存角色:', values);
      message.success(editingRole ? '角色更新成功' : '角色创建成功');
      setRoleModalVisible(false);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const userColumns = [
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
      width: 120,
    },
    {
      title: '姓名',
      dataIndex: 'fullName',
      key: 'fullName',
      width: 120,
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
      width: 200,
    },
    {
      title: '角色',
      dataIndex: 'roles',
      key: 'roles',
      width: 150,
      render: (roles: string[]) => (
        <div>
          {roles.map(role => (
            <Tag key={role} color="blue">{role}</Tag>
          ))}
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: string) => (
        <Tag color={status === 'ACTIVE' ? 'green' : 'red'}>
          {status === 'ACTIVE' ? '活跃' : '停用'}
        </Tag>
      ),
    },
    {
      title: '最后登录',
      dataIndex: 'lastLogin',
      key: 'lastLogin',
      width: 150,
      render: (date: string) => date.substring(0, 16).replace('T', ' '),
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_, record: User) => (
        <Space>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEditUser(record)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Button
              type="text"
              icon={<DeleteOutlined />}
              onClick={() => handleDeleteUser(record)}
              disabled={record.username === 'admin'}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const roleColumns = [
    {
      title: '角色代码',
      dataIndex: 'code',
      key: 'code',
      width: 120,
    },
    {
      title: '角色名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      width: 200,
    },
    {
      title: '权限数量',
      dataIndex: 'permissions',
      key: 'permissions',
      width: 100,
      render: (permissions: string[]) => permissions.length,
    },
    {
      title: '用户数量',
      dataIndex: 'userCount',
      key: 'userCount',
      width: 100,
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_, record: Role) => (
        <Space>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEditRole(record)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Button
              type="text"
              icon={<DeleteOutlined />}
              disabled={record.userCount > 0}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Tabs defaultActiveKey="system">
        <TabPane tab={<span><SettingOutlined />系统配置</span>} key="system">
          <Card>
            <Form
              form={form}
              layout="vertical"
              onFinish={handleSaveSystemConfig}
              initialValues={{
                systemName: '项目协同管理系统',
                companyName: '某某科技有限公司',
                systemVersion: 'v1.0.0',
                maxUsers: 100,
                sessionTimeout: 30,
                enableAuditLog: true,
                enableNotification: true,
                enableBackup: true,
                backupFrequency: 'daily',
              }}
            >
              <Title level={4}>基本信息</Title>
              <div className="grid grid-cols-2 gap-4">
                <Form.Item
                  name="systemName"
                  label="系统名称"
                  rules={[{ required: true, message: '请输入系统名称' }]}
                >
                  <Input />
                </Form.Item>
                <Form.Item
                  name="companyName"
                  label="公司名称"
                  rules={[{ required: true, message: '请输入公司名称' }]}
                >
                  <Input />
                </Form.Item>
                <Form.Item
                  name="systemVersion"
                  label="系统版本"
                >
                  <Input disabled />
                </Form.Item>
                <Form.Item
                  name="maxUsers"
                  label="最大用户数"
                >
                  <InputNumber min={1} max={1000} style={{ width: '100%' }} />
                </Form.Item>
              </div>

              <Divider />

              <Title level={4}>安全设置</Title>
              <div className="grid grid-cols-2 gap-4">
                <Form.Item
                  name="sessionTimeout"
                  label="会话超时时间（分钟）"
                >
                  <InputNumber min={5} max={480} style={{ width: '100%' }} />
                </Form.Item>
                <Form.Item
                  name="passwordPolicy"
                  label="密码策略"
                >
                  <Select>
                    <Option value="simple">简单</Option>
                    <Option value="medium">中等</Option>
                    <Option value="complex">复杂</Option>
                  </Select>
                </Form.Item>
              </div>

              <Divider />

              <Title level={4}>功能开关</Title>
              <div className="space-y-4">
                <Form.Item
                  name="enableAuditLog"
                  label="启用审计日志"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
                <Form.Item
                  name="enableNotification"
                  label="启用系统通知"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
                <Form.Item
                  name="enableBackup"
                  label="启用自动备份"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
                <Form.Item
                  name="backupFrequency"
                  label="备份频率"
                >
                  <Select>
                    <Option value="hourly">每小时</Option>
                    <Option value="daily">每天</Option>
                    <Option value="weekly">每周</Option>
                  </Select>
                </Form.Item>
              </div>

              <Divider />

              <Form.Item>
                <Space>
                  <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
                    保存配置
                  </Button>
                  <Button icon={<ReloadOutlined />}>
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </Card>
        </TabPane>

        <TabPane tab={<span><UserOutlined />用户管理</span>} key="users">
          <Card>
            <div className="flex justify-between items-center mb-4">
              <Title level={4} className="mb-0">用户列表</Title>
              <Button type="primary" icon={<PlusOutlined />} onClick={handleCreateUser}>
                新建用户
              </Button>
            </div>
            <Table
              columns={userColumns}
              dataSource={users}
              rowKey="id"
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
              }}
            />
          </Card>
        </TabPane>

        <TabPane tab={<span><SecurityScanOutlined />角色权限</span>} key="roles">
          <Card>
            <div className="flex justify-between items-center mb-4">
              <Title level={4} className="mb-0">角色列表</Title>
              <Button type="primary" icon={<PlusOutlined />} onClick={handleCreateRole}>
                新建角色
              </Button>
            </div>
            <Table
              columns={roleColumns}
              dataSource={roles}
              rowKey="id"
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
              }}
            />
          </Card>
        </TabPane>

        <TabPane tab={<span><DatabaseOutlined />数据管理</span>} key="data">
          <Card>
            <Alert
              message="数据管理"
              description="管理系统数据备份、恢复和清理等操作。"
              type="info"
              showIcon
              className="mb-4"
            />
            <div className="space-y-4">
              <Card size="small" title="数据备份">
                <div className="flex justify-between items-center">
                  <div>
                    <Text>最后备份时间: 2025-01-14 02:00:00</Text>
                    <br />
                    <Text type="secondary">备份文件大小: 2.3GB</Text>
                  </div>
                  <Space>
                    <Button>立即备份</Button>
                    <Button>下载备份</Button>
                  </Space>
                </div>
              </Card>
              <Card size="small" title="数据恢复">
                <div className="flex justify-between items-center">
                  <Text>从备份文件恢复系统数据</Text>
                  <Upload>
                    <Button icon={<UploadOutlined />}>选择备份文件</Button>
                  </Upload>
                </div>
              </Card>
              <Card size="small" title="数据清理">
                <div className="flex justify-between items-center">
                  <div>
                    <Text>清理90天前的日志和临时文件</Text>
                    <br />
                    <Text type="secondary">预计释放空间: 1.2GB</Text>
                  </div>
                  <Button danger>开始清理</Button>
                </div>
              </Card>
            </div>
          </Card>
        </TabPane>

        <TabPane tab={<span><BellOutlined />通知设置</span>} key="notifications">
          <Card>
            <Alert
              message="通知设置"
              description="配置系统通知的发送方式和规则。"
              type="info"
              showIcon
              className="mb-4"
            />
            <Form layout="vertical">
              <Title level={4}>邮件通知</Title>
              <div className="grid grid-cols-2 gap-4">
                <Form.Item label="SMTP服务器">
                  <Input placeholder="smtp.company.com" />
                </Form.Item>
                <Form.Item label="端口">
                  <InputNumber placeholder={587} style={{ width: '100%' }} />
                </Form.Item>
                <Form.Item label="用户名">
                  <Input placeholder="<EMAIL>" />
                </Form.Item>
                <Form.Item label="密码">
                  <Input.Password placeholder="邮箱密码" />
                </Form.Item>
              </div>

              <Divider />

              <Title level={4}>通知规则</Title>
              <div className="space-y-4">
                <Form.Item label="项目延期通知" valuePropName="checked">
                  <Switch defaultChecked />
                </Form.Item>
                <Form.Item label="KPI异常通知" valuePropName="checked">
                  <Switch defaultChecked />
                </Form.Item>
                <Form.Item label="结算确认通知" valuePropName="checked">
                  <Switch defaultChecked />
                </Form.Item>
                <Form.Item label="数据质量警告" valuePropName="checked">
                  <Switch defaultChecked />
                </Form.Item>
              </div>

              <Form.Item>
                <Button type="primary" icon={<SaveOutlined />}>
                  保存设置
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </TabPane>
      </Tabs>

      {/* 用户编辑模态框 */}
      <Modal
        title={editingUser ? '编辑用户' : '新建用户'}
        open={userModalVisible}
        onOk={handleSaveUser}
        onCancel={() => setUserModalVisible(false)}
        width={600}
      >
        <Form form={userForm} layout="vertical">
          <div className="grid grid-cols-2 gap-4">
            <Form.Item
              name="username"
              label="用户名"
              rules={[{ required: true, message: '请输入用户名' }]}
            >
              <Input />
            </Form.Item>
            <Form.Item
              name="fullName"
              label="姓名"
              rules={[{ required: true, message: '请输入姓名' }]}
            >
              <Input />
            </Form.Item>
            <Form.Item
              name="email"
              label="邮箱"
              rules={[
                { required: true, message: '请输入邮箱' },
                { type: 'email', message: '请输入有效的邮箱地址' },
              ]}
            >
              <Input />
            </Form.Item>
            <Form.Item
              name="roles"
              label="角色"
              rules={[{ required: true, message: '请选择角色' }]}
            >
              <Select mode="multiple">
                <Option value="ADMIN">系统管理员</Option>
                <Option value="PROJECT_MANAGER">项目经理</Option>
                <Option value="FINANCE_MANAGER">财务经理</Option>
              </Select>
            </Form.Item>
          </div>
          {!editingUser && (
            <div className="grid grid-cols-2 gap-4">
              <Form.Item
                name="password"
                label="密码"
                rules={[{ required: true, message: '请输入密码' }]}
              >
                <Input.Password />
              </Form.Item>
              <Form.Item
                name="confirmPassword"
                label="确认密码"
                rules={[{ required: true, message: '请确认密码' }]}
              >
                <Input.Password />
              </Form.Item>
            </div>
          )}
          <Form.Item
            name="status"
            label="状态"
            valuePropName="checked"
          >
            <Switch checkedChildren="启用" unCheckedChildren="停用" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 角色编辑模态框 */}
      <Modal
        title={editingRole ? '编辑角色' : '新建角色'}
        open={roleModalVisible}
        onOk={handleSaveRole}
        onCancel={() => setRoleModalVisible(false)}
        width={600}
      >
        <Form form={roleForm} layout="vertical">
          <Form.Item
            name="code"
            label="角色代码"
            rules={[{ required: true, message: '请输入角色代码' }]}
          >
            <Input placeholder="ROLE_NAME" />
          </Form.Item>
          <Form.Item
            name="name"
            label="角色名称"
            rules={[{ required: true, message: '请输入角色名称' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="description"
            label="角色描述"
          >
            <Input.TextArea rows={3} />
          </Form.Item>
          <Form.Item
            name="permissions"
            label="权限"
            rules={[{ required: true, message: '请选择权限' }]}
          >
            <Select mode="multiple" placeholder="选择权限">
              <Option value="project:read">项目查看</Option>
              <Option value="project:write">项目编辑</Option>
              <Option value="kpi:read">KPI查看</Option>
              <Option value="kpi:write">KPI编辑</Option>
              <Option value="settlement:read">结算查看</Option>
              <Option value="settlement:write">结算编辑</Option>
              <Option value="financial:read">财务查看</Option>
              <Option value="user:manage">用户管理</Option>
              <Option value="system:config">系统配置</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Settings;
