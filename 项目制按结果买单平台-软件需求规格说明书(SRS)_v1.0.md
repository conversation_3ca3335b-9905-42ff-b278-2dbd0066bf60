# 项目制按结果买单平台-软件需求规格说明书(SRS)

## 文档信息

| 项目 | 内容 |
|------|------|
| 文档名称 | 项目制按结果买单平台-软件需求规格说明书(SRS) |
| 版本 | v1.0 |
| 拟上线模式 | SaaS + 私有化可选 |
| 适用行业 | 非标设备制造、电子行业、系统集成、工程服务等 |
| 作者/评审 | 产品/架构/实施/法务/财务共同评审 |
| 更新记录 | v0.9草案 → v1.0正式版 |
| 创建日期 | 2025-01-14 |

## 目录

1. [背景与目标](#1-背景与目标)
2. [术语与角色](#2-术语与角色)
3. [业务流程](#3-业务流程)
4. [功能需求](#4-功能需求)
5. [非功能需求](#5-非功能需求)
6. [指标与计算口径](#6-指标与计算口径)
7. [结算规则引擎](#7-结算规则引擎)
8. [数据模型](#8-数据模型)
9. [接口与集成](#9-接口与集成)
10. [UI与交互设计](#10-ui与交互设计)
11. [流程与状态机](#11-流程与状态机)
12. [权限矩阵](#12-权限矩阵)
13. [异常与边界处理](#13-异常与边界处理)
14. [技术架构](#14-技术架构)
15. [实施计划](#15-实施计划)
16. [验收标准](#16-验收标准)
17. [风险评估](#17-风险评估)
18. [附录](#18-附录)

---

## 1. 背景与目标

### 1.1 项目背景

#### 1.1.1 行业现状与痛点

**传统项目管理痛点：**
- **项目周期长、变更频繁**：平均项目周期6-18个月，变更率高达40-60%
- **费用复杂、利润难核算**：成本分散在多个系统，缺乏统一视图
- **数据孤岛严重**：合同在CRM、成本在ERP、进度在Excel、沟通在微信群
- **进度失控、成本超支**：缺乏实时监控，问题发现滞后
- **利润不清、回款慢**：无法准确计算项目真实盈利能力

**具体业务场景：**
1. **非标设备制造**：客户需求变更频繁，材料价格波动大，工期延误常见
2. **电子行业**：技术迭代快，元器件价格敏感，质量要求高
3. **系统集成**：多供应商协调复杂，现场实施变数多
4. **工程服务**：人力成本占比高，工时统计困难，服务质量难量化

#### 1.1.2 市场机会

- **市场规模**：中国项目型制造业市场规模约2万亿元
- **数字化渗透率**：目前仅15%，存在巨大提升空间
- **政策支持**：工信部推动制造业数字化转型，相关政策扶持力度加大
- **技术成熟度**：云计算、大数据、AI技术为解决方案提供技术基础

### 1.2 产品目标

#### 1.2.1 核心价值主张

**"可审计的结果KPI+结算引擎"**将提效、降本、加速回款等收益转换成可对账的成果费，支持"低保底+绩效分成/共享节省"的商业模式。

#### 1.2.2 具体目标

**业务目标：**
- 项目毛利率提升10-30%
- 项目交付准时率提升至95%以上
- 回款周期缩短20-40%
- 变更处理效率提升50%
- 数据准确性达到98%以上

**技术目标：**
- 统一数据模型连接合同-项目-采购-工时-质量-财务
- 建立可追溯的基线与KPI计算口径
- 实现自动化结算与对账
- 提供实时数据健康监控

**商业目标：**
- 将"结果改进"转为可结算的成果费
- 按月/季度自动出对账单、发票建议
- 支持合规审计与风控
- 建立可持续的合作伙伴关系

### 1.3 项目范围

#### 1.3.1 MVP阶段（90天）

**核心功能：**
- 项目与合同管理
- 基线冻结与版本管理
- KPI定义与计算引擎
- 数据集成（Excel/CSV + 金蝶/用友基本对接）
- 变更管理闭环
- 成果计费与对账
- 数据健康评分
- 基础仪表盘与导出

**技术实现：**
- 基础架构搭建
- 核心业务模块开发
- 基本集成接口
- 用户权限管理
- 基础报表功能

#### 1.3.2 Phase 2（6个月）

**扩展功能：**
- 银行对账/回款预测
- 电子发票对接
- 移动端工时App
- 供应商与质量模块
- 移动审批流程
- 高级报表与分析

#### 1.3.3 Phase 3（12个月）

**高级功能：**
- 预测性分析（EAC）
- 成本驱动分解
- AI数据对齐与异常检测
- 行业模板库
- 智能决策支持
- 高级集成能力

---

## 2. 术语与角色

### 2.1 核心术语定义

| 术语 | 英文 | 定义 | 示例 |
|------|------|------|------|
| KPI | Key Performance Indicator | 关键结果指标，用于衡量项目绩效 | OTD、毛利率、DSO、罚款/加急费、PPV、返工率 |
| 基线 | Baseline | 上线前确认的指标口径与起点，用于衡量改进效果 | 历史6个月平均毛利率15%作为基线 |
| 成果费 | Performance Fee | 基于经双方确认的新增毛利/节省/资金收益等计算的绩效分成 | 新增毛利的15%作为成果费 |
| 变更/签证 | Change/Visa | 客户提出的需求变更与现场签证单，影响成本/工期/收入 | 增加功能模块，成本增加10万 |
| OTD | On Time Delivery | 准时交付率 | 承诺10个里程碑，实际准时完成9个，OTD=90% |
| PPV | Purchase Price Variance | 采购价格差异 | 标准价100元，实际采购95元，PPV=-5元 |
| DSO | Days Sales Outstanding | 应收账款周转天数 | 应收100万，月销售额50万，DSO=60天 |
| EAC | Estimate at Completion | 完工估算 | 项目预算100万，已花费60万，预计还需50万 |
| WIP | Work in Progress | 在制品 | 已投入成本但未确认收入的项目 |

### 2.2 业务术语

| 术语 | 定义 | 业务含义 |
|------|------|----------|
| 重基线 | 因外部环境重大变化需要重新设定基准值 | 原材料价格波动超过±8%时触发 |
| 数据健康评分 | 数据质量的综合评估指标 | 完整率、时效性、一致性、异常率的加权平均 |
| 可控/不可控成本 | 区分企业可控制和不可控制的成本因素 | 人工效率可控，原材料涨价不可控 |
| 共享节省 | 双方共同分享成本节省带来的收益 | 节省10万成本，按3:7比例分享 |
| 负向保护 | 避免因绩效下降产生负成果费 | 成果费最低为0，不扣减保底费用 |
| 审计轨迹 | 完整的数据变更和操作记录 | 每个数据的来源、时间、操作人可追溯 |

### 2.3 系统角色定义

#### 2.3.1 内部角色

| 角色 | 英文简称 | 主要职责 | 权限范围 | 典型用户 |
|------|----------|----------|----------|----------|
| 系统管理员 | Admin | 系统配置、用户管理、数据源配置 | 全系统权限 | IT部门负责人 |
| 项目经理 | PM | 项目执行、变更管理、进度跟踪 | 所负责项目的全部数据 | 项目部经理 |
| 采购/供应链 | SCM | 采购数据录入、供应商管理、成本控制 | 采购相关数据读写 | 采购部门员工 |
| 财务/结算 | FIN/CFO | 财务数据审核、成本核算、结算确认 | 财务数据查看、结算审批 | 财务部门负责人 |
| 质量/工程 | QA/ENG | 质量数据录入、技术变更评估 | 质量工程数据读写 | 质量工程师 |
| 销售/商务 | Sales/BD | 合同管理、客户沟通、商务谈判 | 合同客户数据读写 | 销售经理 |
| 审计员 | Auditor | 数据审计、合规检查 | 全系统只读+导出 | 内审部门 |

#### 2.3.2 外部角色

| 角色 | 权限范围 | 主要功能 | 访问方式 |
|------|----------|----------|----------|
| 客户协同 | 指定项目只读+审批 | 查看对账单、审批变更、电子签名 | 客户门户 |
| 供应商 | 相关采购订单 | 确认订单、更新交期、上传发票 | 供应商门户 |
| 第三方审计 | 审计包下载 | 获取审计数据包 | API接口 |

### 2.4 组织架构支持

#### 2.4.1 多租户架构

```
集团公司 (Tenant)
├── 事业部A (Organization)
│   ├── 项目部 (Department)
│   ├── 采购部 (Department)
│   └── 财务部 (Department)
├── 事业部B (Organization)
│   ├── 研发部 (Department)
│   └── 生产部 (Department)
└── 共享服务中心 (Organization)
    ├── IT部 (Department)
    └── 人力资源部 (Department)
```

#### 2.4.2 数据隔离策略

- **租户级隔离**：不同企业数据完全隔离
- **组织级隔离**：同一企业不同事业部数据隔离
- **项目级隔离**：项目成员只能访问相关项目数据
- **字段级隔离**：敏感字段（如价格、利润）按角色控制

---

## 3. 业务流程

### 3.1 总体业务流程图

```mermaid
graph TD
    A[客户接入] --> B[数据源连接]
    B --> C[字段映射配置]
    C --> D[基线计算与冻结]
    D --> E[项目执行监控]
    E --> F[变更管理]
    E --> G[数据采集与健康检查]
    G --> H[KPI计算]
    H --> I[成果费计算]
    I --> J[对账确认]
    J --> K[开票回款]
    F --> E
    L[重基线触发] --> D
```

### 3.2 详细业务流程

#### 3.2.1 客户接入与基线冻结流程

**阶段1：数据源接入（1-2周）**

1. **需求调研**
   - 收集客户现有系统清单（ERP、CRM、OA等）
   - 确定数据源访问权限和接口方式
   - 评估数据质量和完整性

2. **系统连接**
   - 配置数据源连接参数
   - 建立安全连接通道
   - 测试数据拉取功能

3. **字段映射**
   - 标准化字段映射配置
   - 数据格式转换规则
   - 异常数据处理规则

**阶段2：指标口径确定（1周）**

1. **KPI选择**
   - 根据行业模板选择核心KPI
   - 自定义特殊业务指标
   - 确定计算维度和频率

2. **口径定义**
   - 明确每个指标的计算公式
   - 定义数据来源和取数规则
   - 设置异常剔除条件

**阶段3：基线计算（1周）**

1. **历史数据分析**
   - 选择基线计算时间窗口（通常6-12个月）
   - 清洗和验证历史数据
   - 计算各KPI的基线值

2. **基线确认**
   - 生成基线计算报告
   - 双方确认基线合理性
   - 电子签名确认基线

**阶段4：生效配置**
   - 设置结算周期（月度/季度）
   - 配置成果费计算公式
   - 设置审批流程和权限

#### 3.2.2 项目执行与数据采集流程

**日常数据采集**

```mermaid
sequenceDiagram
    participant ERP as ERP系统
    participant ETL as ETL服务
    participant DB as 数据库
    participant Engine as 计算引擎
    participant Alert as 告警系统

    ERP->>ETL: T+1数据同步
    ETL->>DB: 数据清洗入库
    DB->>Engine: 触发健康检查
    Engine->>Alert: 异常数据告警
    Engine->>DB: 更新健康评分
```

**数据质量监控**

1. **完整性检查**
   - 必填字段缺失检测
   - 关联数据一致性验证
   - 业务逻辑合理性校验

2. **时效性检查**
   - 数据更新及时性监控
   - 延迟数据告警机制
   - 补录数据流程

3. **准确性检查**
   - 数据格式验证
   - 数值范围合理性检查
   - 重复数据识别

#### 3.2.3 变更管理闭环流程

**变更单生命周期**

```mermaid
stateDiagram-v2
    [*] --> 草稿
    草稿 --> 待评估: 提交评估
    待评估 --> 待客户确认: 完成评估
    待评估 --> 草稿: 退回修改
    待客户确认 --> 已确认: 客户同意
    待客户确认 --> 已拒绝: 客户拒绝
    已确认 --> 执行中: 开始执行
    执行中 --> 已完成: 执行完成
    已完成 --> 关闭: 影响确认
    已拒绝 --> [*]
    关闭 --> [*]
```

**变更影响评估**

1. **成本影响**
   - 材料成本变化
   - 人工成本变化
   - 外协成本变化
   - 其他费用变化

2. **工期影响**
   - 关键路径分析
   - 里程碑调整
   - 资源重新分配

3. **质量影响**
   - 技术风险评估
   - 质量标准调整
   - 测试计划更新

#### 3.2.4 结果计算与对账结算流程

**月度/季度结算流程**

1. **数据准备**
   - 数据健康检查（评分≥80分）
   - 异常数据标记和处理
   - 基线有效性确认

2. **KPI计算**
   - 按配置公式计算各项KPI
   - 可控/不可控因素分离
   - 异常剔除处理

3. **成果费计算**
   - 应用结算公式
   - 封顶和保底处理
   - 负向保护机制

4. **对账确认**
   - 生成详细对账单
   - 双方确认签字
   - 开票申请提交

5. **开票回款**
   - 电子发票开具
   - 回款状态跟踪
   - 应收账款管理

---

## 4. 功能需求

### 4.1 功能需求总览

| 编号 | 功能模块 | 优先级 | 复杂度 | 预估工期 |
|------|----------|--------|--------|----------|
| FR-01 | 组织与权限管理 | M | 中 | 2周 |
| FR-02 | 项目与合同管理 | M | 高 | 3周 |
| FR-03 | KPI定义与结果引擎 | M | 高 | 4周 |
| FR-04 | 基线管理 | M | 中 | 2周 |
| FR-05 | 数据集成与健康监控 | M | 高 | 4周 |
| FR-06 | 成本与毛利核算 | M | 高 | 3周 |
| FR-07 | 变更/签证管理 | M | 中 | 2周 |
| FR-08 | 绩效计费与结算引擎 | M | 高 | 4周 |
| FR-09 | 开票与回款跟踪 | S | 中 | 2周 |
| FR-10 | 报表与仪表盘 | M | 中 | 3周 |
| FR-11 | 审批流与SLA | S | 中 | 2周 |
| FR-12 | 审计与留痕 | M | 中 | 2周 |
| FR-13 | 多币种与税务 | S | 中 | 1周 |
| FR-14 | 客户协同门户 | C | 中 | 3周 |

**优先级说明：**
- M (Must): MVP必须功能
- S (Should): 重要功能，Phase 2实现
- C (Could): 可选功能，Phase 3实现

### 4.2 详细功能需求

#### FR-01 组织与权限管理 (M)

**功能描述：**
支持多企业、多组织架构的权限管理体系，确保数据安全和访问控制。

**详细需求：**

1. **多租户管理**
   - 支持集团公司下多个子公司/事业部独立管理
   - 租户间数据完全隔离，无法跨租户访问
   - 支持租户级别的功能开关和配置

2. **组织架构管理**
   - 支持多层级组织架构（公司-事业部-部门-项目组）
   - 组织架构变更历史记录
   - 支持矩阵式组织结构

3. **角色权限管理**
   - 基于RBAC模型的权限控制
   - 支持功能权限和数据权限分离
   - 角色模板和自定义角色
   - 权限继承和覆盖机制

4. **用户管理**
   - 用户生命周期管理（创建、激活、停用、删除）
   - 支持批量用户导入
   - 用户角色分配和权限查看
   - 登录日志和操作审计

5. **单点登录集成**
   - 支持企业微信、钉钉、飞书SSO
   - 支持LDAP/AD域集成
   - 支持SAML 2.0和OAuth 2.0协议

**验收标准：**
- 不同事业部用户无法访问其他事业部数据
- 审计员角色只能查看和导出，无法修改任何数据
- SSO登录成功率≥99%
- 权限变更实时生效

#### FR-02 项目与合同管理 (M)

**功能描述：**
建立项目与合同的关联关系，支持复杂的项目结构和合同条款管理。

**详细需求：**

1. **合同管理**
   - 合同基本信息：编号、名称、客户、金额、币种、税率、签订日期、生效期间
   - 合同条款配置：付款条件、质保期、违约条款、变更条款
   - 绩效条款：保底费用、分成比例、封顶金额、结算周期
   - 合同附件管理：PDF文档、扫描件、电子签名

2. **项目管理**
   - 项目基本信息：编号、名称、类型、状态、预算、实际成本
   - 项目分类：按行业、规模、复杂度、风险等级分类
   - 项目团队：项目经理、核心成员、外部顾问
   - 项目关联：支持一个合同多个项目，一个项目多个子项目

3. **里程碑管理**
   - 里程碑定义：名称、计划日期、实际日期、权重、状态
   - 里程碑类型：设计完成、采购完成、生产完成、测试完成、交付验收
   - OTD计算：准时交付率统计和分析
   - 里程碑预警：延期风险提醒

4. **项目预算管理**
   - 预算科目：材料费、人工费、外协费、差旅费、其他费用
   - 预算版本：初始预算、调整预算、最新预算
   - 预算执行：实际发生额、预算余额、执行率
   - 预算控制：超预算预警和审批

**验收标准：**
- 支持1个合同关联多个项目的复杂结构
- 里程碑OTD计算准确率100%
- 预算超支自动预警，响应时间<1分钟
- 合同条款变更全程留痕

#### FR-03 KPI定义与结果引擎 (M)

**功能描述：**
提供灵活的KPI定义和计算引擎，支持复杂的业务规则和计算逻辑。

**详细需求：**

1. **KPI定义管理**
   - KPI基本信息：名称、描述、分类、单位、目标值
   - 计算公式：支持四则运算、函数、条件判断
   - 数据来源：指定数据表、字段、过滤条件
   - 计算维度：项目、客户、时间、产品线等
   - 版本管理：公式变更历史和版本对比

2. **常用KPI模板**
   - **毛利率**：(收入-成本)/收入×100%
   - **OTD准时交付率**：准时完成里程碑数/总里程碑数×100%
   - **成本偏差率**：(实际成本-预算成本)/预算成本×100%
   - **PPV采购价差**：实际采购价-标准采购价
   - **DSO应收账款天数**：应收账款余额/平均日销售额
   - **返工率**：返工成本/总成本×100%
   - **资金收益**：释放资金×资金成本率×天数/365

3. **计算引擎**
   - 批量计算：支持大数据量的并行计算
   - 实时计算：关键指标实时更新
   - 增量计算：只计算变更部分，提高效率
   - 异常处理：计算错误自动重试和告警

4. **异常剔除规则**
   - 不可控因素：原材料价格波动、汇率变动、政策变化
   - 客户原因：需求变更、延期确认、付款延迟
   - 不可抗力：自然灾害、疫情、停电等
   - 数据异常：明显错误的数据自动标记

**验收标准：**
- 支持至少20种常用KPI模板
- 计算引擎处理100万条记录≤4小时
- 公式语法错误实时提示
- 异常剔除规则覆盖率≥90%

#### FR-04 基线管理 (M)

**功能描述：**
建立可靠的基线管理机制，为绩效改进提供准确的对比基准。

**详细需求：**

1. **基线计算**
   - 历史窗口选择：3个月、6个月、12个月可选
   - 数据清洗：剔除异常值和不完整数据
   - 统计方法：平均值、中位数、加权平均可选
   - 季节性调整：考虑业务季节性波动

2. **基线冻结**
   - 冻结流程：计算→审核→确认→冻结
   - 电子签名：双方确认基线的电子签名
   - 冻结版本：基线版本号和冻结时间
   - 权限控制：只有授权人员可以冻结基线

3. **重基线管理**
   - 触发条件：原材料价格波动>±8%、汇率变动>±5%、政策重大变化
   - 重基线流程：申请→评估→审批→重新计算→确认
   - 影响分析：重基线对历史绩效的影响分析
   - 版本对比：新旧基线的差异对比

4. **基线监控**
   - 基线有效性：定期检查基线是否仍然有效
   - 偏差预警：实际值与基线偏差超过阈值时预警
   - 趋势分析：基线变化趋势和原因分析

**验收标准：**
- 基线计算准确率100%
- 重基线触发条件准确识别
- 基线冻结流程完整留痕
- 支持基线版本回溯查询

#### FR-05 数据集成与健康监控 (M)

**功能描述：**
建立统一的数据集成平台，确保数据质量和系统可靠性。

**详细需求：**

1. **数据源集成**
   - **ERP系统**：金蝶K3/云星空、用友U8/NC、SAP
   - **财务数据**：总账、应收、应付、存货、成本
   - **业务数据**：销售订单、采购订单、生产工单、库存
   - **文件导入**：Excel、CSV、TXT格式支持
   - **API接口**：RESTful API、WebService、数据库直连

2. **数据映射配置**
   - 字段映射：源系统字段与标准字段的映射关系
   - 数据转换：格式转换、单位换算、编码转换
   - 业务规则：数据验证规则和业务逻辑检查
   - 映射模板：常用ERP系统的预置映射模板

3. **数据同步策略**
   - 同步频率：实时、小时、日、周可配置
   - 同步方式：全量同步、增量同步、变更同步
   - 失败重试：自动重试机制和人工干预
   - 数据缓存：提高查询性能的数据缓存策略

4. **数据健康监控**
   - **完整性检查**：必填字段缺失率、关联数据完整性
   - **时效性检查**：数据更新及时性、延迟数据统计
   - **一致性检查**：跨系统数据一致性验证
   - **准确性检查**：数据格式、数值范围、逻辑合理性
   - **健康评分**：综合评分算法，权重可配置

5. **异常处理**
   - 异常检测：自动识别异常数据和模式
   - 异常分类：数据缺失、格式错误、逻辑异常、系统异常
   - 处理策略：自动修复、人工处理、忽略处理
   - 异常报告：异常统计报告和趋势分析

**验收标准：**
- 支持主流ERP系统数据集成
- 数据同步成功率≥99%
- 数据健康评分算法准确性≥95%
- 异常数据检测准确率≥90%

#### FR-06 成本与毛利核算 (M)

**功能描述：**
建立精确的项目成本核算体系，支持多维度的毛利分析。

**详细需求：**

1. **成本归集**
   - **直接材料**：原材料、外购件、标准件成本
   - **直接人工**：项目人员工时成本、外包人工成本
   - **制造费用**：设备折旧、水电费、车间管理费
   - **其他费用**：差旅费、运输费、保险费、质量损失

2. **成本分摊**
   - 分摊基础：工时、机时、材料成本、直接费用
   - 分摊方法：直接分摊、比例分摊、作业成本法
   - 共同成本：多项目共享资源的成本分摊
   - 期间费用：管理费用、销售费用的合理分摊

3. **毛利计算**
   - 项目毛利：项目收入 - 项目成本
   - 毛利率：项目毛利 / 项目收入 × 100%
   - 分阶段毛利：按项目阶段计算阶段毛利
   - 预测毛利：基于完工进度的毛利预测

4. **PPV分析**
   - 价格差异：实际采购价 - 标准价格
   - 数量差异：实际用量 - 标准用量
   - 可控分析：区分可控和不可控的价格差异
   - 供应商分析：按供应商统计PPV情况

5. **成本控制**
   - 预算对比：实际成本与预算成本对比
   - 超支预警：成本超支自动预警机制
   - 成本趋势：成本变化趋势分析
   - 成本优化：成本节约机会识别

**验收标准：**
- 成本归集准确率≥98%
- 毛利计算与财务系统差异≤0.5%
- PPV分析覆盖所有采购物料
- 成本超支预警及时率100%

#### FR-07 变更/签证管理 (M)

**功能描述：**
建立完整的变更管理闭环，确保变更影响准确评估和及时处理。

**详细需求：**

1. **变更单管理**
   - 变更类型：需求变更、设计变更、技术变更、商务变更
   - 变更来源：客户要求、技术优化、成本优化、风险规避
   - 变更内容：详细描述、技术方案、实施计划
   - 附件管理：技术文档、图纸、客户邮件、现场照片

2. **影响评估**
   - **成本影响**：材料成本、人工成本、外协成本变化
   - **工期影响**：关键路径分析、里程碑调整
   - **质量影响**：技术风险、质量标准变化
   - **资源影响**：人员调配、设备占用、供应商变更

3. **审批流程**
   - 内部审批：技术评审、商务评审、管理层审批
   - 客户确认：变更方案确认、费用确认、工期确认
   - 电子签名：支持电子签名和手写签名
   - 审批时限：各环节审批时限设置和超时提醒

4. **执行跟踪**
   - 执行计划：变更实施的详细计划
   - 进度跟踪：变更执行进度实时跟踪
   - 结果确认：变更执行结果确认
   - 影响回写：变更影响自动回写到项目数据

5. **变更统计**
   - 变更统计：按类型、来源、状态统计变更情况
   - 变更分析：变更原因分析和改进建议
   - 变更趋势：变更频率和影响趋势分析

**验收标准：**
- 变更流程闭环率100%
- 影响评估准确率≥95%
- 客户确认平均时长≤3天
- 变更影响自动回写成功率100%

#### FR-08 绩效计费与结算引擎 (M)

**功能描述：**
建立灵活的绩效计费模型，支持复杂的结算规则和自动化结算。

**详细需求：**

1. **计费模型配置**
   - **共享节省模型**：节省金额按比例分成
   - **增益分享模型**：新增收益按比例分成
   - **阶梯奖励模型**：按改进幅度设置不同奖励比例
   - **混合模型**：多种模型组合使用

2. **结算公式引擎**
   - 公式编辑器：可视化公式编辑界面
   - 语法检查：公式语法正确性检查
   - 模拟计算：公式效果模拟和测试
   - 版本管理：公式版本控制和变更历史

3. **结算规则**
   - **封顶机制**：成果费上限设置（如不超过订阅费2.5倍）
   - **保底机制**：最低成果费保障
   - **负向保护**：避免产生负成果费
   - **最低改进**：设置最低改进阈值

4. **结算计算**
   - 自动计算：按设定周期自动计算成果费
   - 手工调整：支持特殊情况的手工调整
   - 计算明细：详细的计算过程和依据
   - 版本对比：不同版本计算结果对比

5. **对账管理**
   - 对账单生成：自动生成详细对账单
   - 对账确认：双方确认对账结果
   - 争议处理：对账争议的处理流程
   - 历史查询：历史对账记录查询

**验收标准：**
- 支持至少5种计费模型
- 结算计算准确率100%
- 对账单自动生成成功率≥99%
- 结算周期准时完成率≥95%

---

## 5. 非功能需求

### 5.1 性能需求 (NFR-01)

**响应时间要求：**
- 用户界面响应时间：≤2秒
- 报表查询响应时间：≤5秒
- 大数据量导出：≤30秒
- API接口响应时间：≤1秒

**吞吐量要求：**
- 并发用户数：≥100用户
- 数据处理能力：单租户日增量100万条记录
- KPI批处理：4小时内完成
- 数据同步频率：支持实时同步

**资源使用：**
- CPU使用率：正常情况下≤70%
- 内存使用率：正常情况下≤80%
- 磁盘I/O：峰值情况下≤80%
- 网络带宽：支持千兆网络

### 5.2 可用性需求 (NFR-02)

**系统可用性：**
- SaaS版本：99.9%月度SLA
- 私有化版本：99.5%月度SLA
- 计划内维护时间：每月≤4小时
- 故障恢复时间：≤1小时

**容灾备份：**
- 数据备份：每日全量备份+实时增量备份
- 异地备份：支持异地容灾备份
- RPO（恢复点目标）：≤15分钟
- RTO（恢复时间目标）：≤1小时

**高可用架构：**
- 关键服务双活部署
- 数据库主从复制
- 负载均衡和故障转移
- 自动健康检查和恢复

### 5.3 安全合规需求 (NFR-03)

**数据安全：**
- 传输加密：TLS 1.3加密传输
- 存储加密：数据库字段级加密
- 国密支持：支持国密SM2/SM3/SM4算法
- 密钥管理：统一密钥管理系统

**访问控制：**
- 身份认证：多因子认证支持
- 权限控制：细粒度权限控制
- 会话管理：安全会话管理
- 审计日志：完整的操作审计日志

**合规要求：**
- 等保二级：参考等保二级标准
- 数据保护：符合数据保护法规
- 日志留存：审计日志7年留存
- 隐私保护：敏感数据脱敏处理

### 5.4 可扩展性需求 (NFR-04)

**水平扩展：**
- 微服务架构：支持服务独立扩展
- 数据库分片：支持数据库水平分片
- 缓存扩展：支持分布式缓存
- 计算扩展：支持计算节点动态扩展

**多租户支持：**
- 租户隔离：数据和计算资源隔离
- 弹性伸缩：根据负载自动伸缩
- 资源配额：租户级资源配额管理
- 性能隔离：租户间性能互不影响

### 5.5 可维护性需求 (NFR-05)

**可观测性：**
- 日志管理：结构化日志和集中管理
- 指标监控：关键业务指标监控
- 链路追踪：分布式链路追踪
- 告警机制：智能告警和通知

**部署运维：**
- 容器化部署：Docker容器化
- 自动化部署：CI/CD自动化流水线
- 灰度发布：支持灰度发布和回滚
- 配置管理：配置即代码管理

### 5.6 本地化需求 (NFR-06)

**多语言支持：**
- 中文为主：简体中文为主要语言
- 英文支持：支持英文界面
- 多时区：支持多时区显示
- 本地化格式：日期、数字、货币格式本地化

**中国特色：**
- 节假日：中国法定节假日支持
- 税务：中国税务制度支持
- 发票：中国电子发票标准
- 银行：中国银行系统对接

---

## 6. 指标与计算口径

### 6.1 核心KPI定义

#### 6.1.1 财务类指标

**1. 毛利率 (Gross Margin Rate)**
```
公式：毛利率 = (项目收入 - 项目成本) / 项目收入 × 100%

项目收入包括：
- 合同收入（含税/不含税可配置）
- 变更收入
- 其他收入

项目成本包括：
- 直接材料成本
- 直接人工成本
- 外协成本
- 运输费用
- 质量损失
- 返工成本

计算维度：项目、客户、产品线、月度/季度
异常剔除：客户原因取消订单的影响、不可控原材料涨价
```

**2. 成本偏差率 (Cost Variance Rate)**
```
公式：成本偏差率 = (实际成本 - 预算成本) / 预算成本 × 100%

实际成本：项目实际发生的全部成本
预算成本：项目批准的预算成本
正值表示超支，负值表示节约

计算维度：项目、成本科目、时间段
异常剔除：客户变更导致的成本增加、不可抗力因素
```

**3. DSO应收账款天数 (Days Sales Outstanding)**
```
公式：DSO = 应收账款余额 / 平均日销售额

应收账款余额：期末应收账款总额
平均日销售额：本期销售收入 / 本期天数

目标：缩短DSO，加速资金回笼
计算维度：客户、项目、业务线
```

#### 6.1.2 运营类指标

**4. OTD准时交付率 (On Time Delivery)**
```
公式：OTD = 准时完成里程碑数 / 总里程碑数 × 100%

准时判定标准：实际完成日期 ≤ 承诺完成日期
里程碑类型：设计、采购、生产、测试、交付
权重设置：不同里程碑可设置不同权重

计算维度：项目、客户、产品类型、月度
异常剔除：客户原因延期、不可抗力延期
```

**5. 返工率 (Rework Rate)**
```
公式：返工率 = 返工成本 / 总项目成本 × 100%

返工成本包括：
- 重新设计成本
- 重新采购成本
- 重新生产成本
- 额外人工成本

计算维度：项目、产品线、质量问题类型
目标：持续降低返工率
```

#### 6.1.3 采购类指标

**6. PPV采购价格差异 (Purchase Price Variance)**
```
公式：PPV = (实际采购单价 - 标准采购单价) × 采购数量

标准采购单价：基线期间的平均采购价格
实际采购单价：当期实际采购价格
正值表示涨价，负值表示降价

可控性判断：
- 可控：供应商选择、采购时机、谈判能力
- 不可控：大宗商品价格波动（超过±8%阈值）

计算维度：物料、供应商、采购员、时间
```

#### 6.1.4 现金流指标

**7. 资金收益 (Cash Flow Benefit)**
```
公式：资金收益 = 释放资金 × 年化资金成本 × 期间天数 / 365

释放资金计算：
- DSO缩短释放的资金 = DSO减少天数 × 平均日销售额
- 库存优化释放的资金 = 库存减少金额
- 付款期延长的资金 = 延长天数 × 平均日采购额

年化资金成本：企业加权平均资本成本（WACC）
典型值：6%-12%

计算维度：项目、资金类型、时间段
```

### 6.2 数据健康评分算法

**综合评分公式：**
```
数据健康分 = W1×完整率 + W2×时效性 + W3×一致性 - W4×异常率

默认权重：
W1 = 0.3 (完整率权重)
W2 = 0.25 (时效性权重)
W3 = 0.25 (一致性权重)
W4 = 0.2 (异常率权重)
```

**分项指标计算：**

1. **完整率 = (1 - 缺失字段数 / 总字段数) × 100%**
   - 核心字段缺失扣分更多
   - 关联数据完整性检查

2. **时效性 = (1 - 延迟更新数据量 / 总数据量) × 100%**
   - 按数据类型设置不同的时效要求
   - 实时数据要求更高

3. **一致性 = (1 - 不一致数据量 / 总数据量) × 100%**
   - 跨系统数据一致性检查
   - 业务逻辑一致性验证

4. **异常率 = 异常数据量 / 总数据量 × 100%**
   - 格式异常、数值异常、逻辑异常
   - 基于历史数据的异常检测

**评分等级：**
- 90-100分：优秀（绿色）
- 80-89分：良好（黄色）
- 70-79分：一般（橙色）
- <70分：较差（红色，限制结算）

### 6.3 可控/不可控因素分离

**可控因素（计入绩效）：**
- 管理效率提升
- 工艺优化改进
- 供应商谈判
- 质量改进
- 流程优化

**不可控因素（剔除计算）：**
- 原材料价格波动（超过±8%）
- 汇率变动（超过±5%）
- 政策法规变化
- 不可抗力事件
- 客户原因变更

**分离方法：**
1. **阈值法**：设置波动阈值，超过阈值的部分视为不可控
2. **基准法**：以基线期间的平均水平为基准
3. **指数法**：参考外部价格指数进行调整
4. **专家判断**：复杂情况下的专家评估

---

## 7. 结算规则引擎

### 7.1 DSL语法设计

**基础语法结构：**
```javascript
// 变量定义
let 新增毛利 = sum(Project.Revenue) - sum(Project.Cost) - Baseline.MarginAbs;
let 罚款节省 = Baseline.Penalty - Current.Penalty;
let 资金收益 = (Baseline.DSO - Current.DSO) * AvgDailySales * FundingCost * Days / 365;

// 成果费计算
let 基础成果费 = 0.15 * 新增毛利 + 0.20 * 罚款节省 + 0.30 * 资金收益;

// 条件判断和封顶
if (数据健康分 < 80) {
    成果费状态 = "待复核";
    可开票 = false;
} else {
    成果费状态 = "正常";
    可开票 = true;
}

// 封顶和保底
let 最终成果费 = min(max(基础成果费, 保底金额), 封顶金额);

// 负保护
最终成果费 = max(最终成果费, 0);

// 重基线触发
if (原材指数变动 > 8%) {
    触发重基线("材料维度");
}
```

### 7.2 预置结算模板

#### 7.2.1 共享节省模型
```javascript
// 适用场景：成本优化项目
let 成本节省 = Baseline.TotalCost - Current.TotalCost;
let 可控节省 = 成本节省 * 可控比例;
let 成果费 = 可控节省 * 分成比例; // 通常20%-30%

// 封顶机制
成果费 = min(成果费, 季度订阅费 * 2.5);
```

#### 7.2.2 增益分享模型
```javascript
// 适用场景：收入提升项目
let 收入增长 = Current.Revenue - Baseline.Revenue;
let 毛利增长 = Current.Margin - Baseline.Margin;
let 成果费 = 毛利增长 * 分成比例; // 通常15%-25%

// 最低改进阈值
if (毛利增长 < 最低改进阈值) {
    成果费 = 0;
}
```

#### 7.2.3 阶梯奖励模型
```javascript
// 适用场景：多目标改进项目
let 改进幅度 = (Current.KPI - Baseline.KPI) / Baseline.KPI;

if (改进幅度 >= 0.2) {
    奖励比例 = 0.3; // 改进20%以上，30%分成
} else if (改进幅度 >= 0.1) {
    奖励比例 = 0.2; // 改进10%-20%，20%分成
} else if (改进幅度 >= 0.05) {
    奖励比例 = 0.1; // 改进5%-10%，10%分成
} else {
    奖励比例 = 0; // 改进不足5%，无奖励
}

let 成果费 = 改进金额 * 奖励比例;
```

#### 7.2.4 综合模型
```javascript
// 多维度综合计算
let 毛利改进费 = 0.15 * (Current.Margin - Baseline.Margin);
let 交期改进费 = 0.10 * 罚款节省;
let 质量改进费 = 0.05 * 返工节省;
let 现金改进费 = 0.20 * 资金收益;

let 总成果费 = 毛利改进费 + 交期改进费 + 质量改进费 + 现金改进费;

// 综合封顶
总成果费 = min(总成果费, 项目总收入 * 0.1);
```

### 7.3 规则引擎架构

**组件设计：**
```
规则引擎
├── 语法解析器 (Parser)
│   ├── 词法分析
│   ├── 语法分析
│   └── 语义检查
├── 执行引擎 (Executor)
│   ├── 表达式计算
│   ├── 条件判断
│   └── 函数调用
├── 数据访问层 (DAL)
│   ├── 数据查询
│   ├── 缓存管理
│   └── 事务控制
└── 监控审计 (Monitor)
    ├── 执行日志
    ├── 性能监控
    └── 错误处理
```

**执行流程：**
1. 规则加载和编译
2. 数据准备和验证
3. 规则执行和计算
4. 结果验证和输出
5. 日志记录和审计

### 7.4 版本管理

**版本控制策略：**
- 语义化版本号：主版本.次版本.修订版本
- 向后兼容：新版本保持向后兼容
- 灰度发布：新规则先在测试环境验证
- 回滚机制：支持快速回滚到上一版本

**变更管理：**
- 变更申请：规则变更需要正式申请
- 影响评估：评估变更对历史数据的影响
- 审批流程：多级审批确保变更合理性
- 生效时间：明确规则生效的时间点

---

## 8. 数据模型

### 8.1 核心实体关系图

```mermaid
erDiagram
    Tenant ||--o{ Organization : contains
    Organization ||--o{ Department : contains
    Department ||--o{ User : belongs_to
    User ||--o{ Project : manages

    Customer ||--o{ Contract : signs
    Contract ||--o{ Project : includes
    Project ||--o{ Milestone : has
    Project ||--o{ Cost : incurs
    Project ||--o{ Revenue : generates
    Project ||--o{ Change : requests

    KpiDef ||--o{ KpiFact : measures
    KpiDef ||--o{ Baseline : establishes
    Project ||--o{ KpiFact : generates

    Contract ||--o{ Settlement : settles
    Settlement ||--o{ Invoice : generates
    Invoice ||--o{ Payment : receives
```

### 8.2 详细数据模型

#### 8.2.1 组织架构相关

**租户表 (Tenant)**
```sql
CREATE TABLE tenant (
    tenant_id VARCHAR(32) PRIMARY KEY,
    tenant_name VARCHAR(100) NOT NULL,
    tenant_code VARCHAR(20) UNIQUE NOT NULL,
    industry_type VARCHAR(50),
    tax_number VARCHAR(50),
    contact_info JSONB,
    subscription_plan VARCHAR(20),
    status VARCHAR(20) DEFAULT 'ACTIVE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**组织表 (Organization)**
```sql
CREATE TABLE organization (
    org_id VARCHAR(32) PRIMARY KEY,
    tenant_id VARCHAR(32) REFERENCES tenant(tenant_id),
    parent_org_id VARCHAR(32) REFERENCES organization(org_id),
    org_name VARCHAR(100) NOT NULL,
    org_code VARCHAR(20) NOT NULL,
    org_type VARCHAR(20), -- 'COMPANY', 'DIVISION', 'DEPARTMENT'
    manager_id VARCHAR(32),
    status VARCHAR(20) DEFAULT 'ACTIVE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**用户表 (User)**
```sql
CREATE TABLE user_account (
    user_id VARCHAR(32) PRIMARY KEY,
    tenant_id VARCHAR(32) REFERENCES tenant(tenant_id),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    employee_id VARCHAR(50),
    department_id VARCHAR(32) REFERENCES organization(org_id),
    phone VARCHAR(20),
    status VARCHAR(20) DEFAULT 'ACTIVE',
    last_login_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 8.2.2 业务核心实体

**客户表 (Customer)**
```sql
CREATE TABLE customer (
    customer_id VARCHAR(32) PRIMARY KEY,
    tenant_id VARCHAR(32) REFERENCES tenant(tenant_id),
    customer_name VARCHAR(200) NOT NULL,
    customer_code VARCHAR(50) NOT NULL,
    customer_type VARCHAR(20), -- 'ENTERPRISE', 'GOVERNMENT', 'INDIVIDUAL'
    industry VARCHAR(50),
    credit_rating VARCHAR(10),
    payment_terms INTEGER, -- 付款天数
    contact_info JSONB,
    address JSONB,
    status VARCHAR(20) DEFAULT 'ACTIVE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**合同表 (Contract)**
```sql
CREATE TABLE contract (
    contract_id VARCHAR(32) PRIMARY KEY,
    tenant_id VARCHAR(32) REFERENCES tenant(tenant_id),
    contract_no VARCHAR(100) UNIQUE NOT NULL,
    contract_name VARCHAR(200) NOT NULL,
    customer_id VARCHAR(32) REFERENCES customer(customer_id),
    contract_amount DECIMAL(15,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'CNY',
    tax_rate DECIMAL(5,4) DEFAULT 0.13,
    sign_date DATE NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    payment_terms JSONB, -- 付款条件
    performance_terms JSONB, -- 绩效条款
    contract_status VARCHAR(20) DEFAULT 'DRAFT',
    sales_manager_id VARCHAR(32) REFERENCES user_account(user_id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**项目表 (Project)**
```sql
CREATE TABLE project (
    project_id VARCHAR(32) PRIMARY KEY,
    tenant_id VARCHAR(32) REFERENCES tenant(tenant_id),
    contract_id VARCHAR(32) REFERENCES contract(contract_id),
    project_no VARCHAR(100) UNIQUE NOT NULL,
    project_name VARCHAR(200) NOT NULL,
    project_type VARCHAR(50),
    project_manager_id VARCHAR(32) REFERENCES user_account(user_id),
    budget_amount DECIMAL(15,2),
    planned_start_date DATE,
    planned_end_date DATE,
    actual_start_date DATE,
    actual_end_date DATE,
    project_status VARCHAR(20) DEFAULT 'PLANNING',
    risk_level VARCHAR(10), -- 'LOW', 'MEDIUM', 'HIGH'
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 8.2.3 KPI和基线相关

**KPI定义表 (KpiDef)**
```sql
CREATE TABLE kpi_definition (
    kpi_id VARCHAR(32) PRIMARY KEY,
    tenant_id VARCHAR(32) REFERENCES tenant(tenant_id),
    kpi_name VARCHAR(100) NOT NULL,
    kpi_code VARCHAR(50) NOT NULL,
    kpi_category VARCHAR(50), -- 'FINANCIAL', 'OPERATIONAL', 'QUALITY'
    description TEXT,
    formula_text TEXT NOT NULL,
    formula_version VARCHAR(20) NOT NULL,
    unit VARCHAR(20),
    target_value DECIMAL(15,4),
    dimensions JSONB, -- 计算维度配置
    exclude_rules JSONB, -- 异常剔除规则
    is_active BOOLEAN DEFAULT TRUE,
    created_by VARCHAR(32) REFERENCES user_account(user_id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**KPI测量表 (KpiFact)**
```sql
CREATE TABLE kpi_fact (
    fact_id VARCHAR(32) PRIMARY KEY,
    tenant_id VARCHAR(32) REFERENCES tenant(tenant_id),
    kpi_id VARCHAR(32) REFERENCES kpi_definition(kpi_id),
    project_id VARCHAR(32) REFERENCES project(project_id),
    measure_period VARCHAR(20), -- '2025Q1', '2025-01'
    dimension_values JSONB, -- 维度值
    measured_value DECIMAL(15,4) NOT NULL,
    is_excluded BOOLEAN DEFAULT FALSE,
    exclude_reason TEXT,
    calculation_trace JSONB, -- 计算轨迹
    data_health_score INTEGER,
    measured_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**基线表 (Baseline)**
```sql
CREATE TABLE baseline (
    baseline_id VARCHAR(32) PRIMARY KEY,
    tenant_id VARCHAR(32) REFERENCES tenant(tenant_id),
    kpi_id VARCHAR(32) REFERENCES kpi_definition(kpi_id),
    baseline_name VARCHAR(100) NOT NULL,
    scope_projects JSONB, -- 适用项目范围
    calculation_window JSONB, -- 计算窗口
    baseline_value DECIMAL(15,4) NOT NULL,
    calculation_method VARCHAR(20), -- 'AVERAGE', 'MEDIAN', 'WEIGHTED'
    version VARCHAR(20) NOT NULL,
    status VARCHAR(20) DEFAULT 'DRAFT',
    frozen_by VARCHAR(32) REFERENCES user_account(user_id),
    frozen_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 8.2.4 成本和收入相关

**成本表 (Cost)**
```sql
CREATE TABLE project_cost (
    cost_id VARCHAR(32) PRIMARY KEY,
    tenant_id VARCHAR(32) REFERENCES tenant(tenant_id),
    project_id VARCHAR(32) REFERENCES project(project_id),
    cost_category VARCHAR(50) NOT NULL, -- 'MATERIAL', 'LABOR', 'OVERHEAD'
    cost_subcategory VARCHAR(50),
    cost_amount DECIMAL(15,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'CNY',
    cost_date DATE NOT NULL,
    document_ref VARCHAR(100), -- 单据号
    source_system VARCHAR(50), -- 来源系统
    supplier_id VARCHAR(32),
    is_controllable BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**收入表 (Revenue)**
```sql
CREATE TABLE project_revenue (
    revenue_id VARCHAR(32) PRIMARY KEY,
    tenant_id VARCHAR(32) REFERENCES tenant(tenant_id),
    project_id VARCHAR(32) REFERENCES project(project_id),
    revenue_type VARCHAR(50), -- 'CONTRACT', 'CHANGE', 'OTHER'
    revenue_amount DECIMAL(15,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'CNY',
    recognition_date DATE NOT NULL,
    invoice_no VARCHAR(100),
    source_system VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 8.2.5 变更管理相关

**变更单表 (Change)**
```sql
CREATE TABLE change_request (
    change_id VARCHAR(32) PRIMARY KEY,
    tenant_id VARCHAR(32) REFERENCES tenant(tenant_id),
    project_id VARCHAR(32) REFERENCES project(project_id),
    change_no VARCHAR(100) UNIQUE NOT NULL,
    change_title VARCHAR(200) NOT NULL,
    change_type VARCHAR(50), -- 'REQUIREMENT', 'DESIGN', 'TECHNICAL'
    change_source VARCHAR(50), -- 'CUSTOMER', 'INTERNAL', 'SUPPLIER'
    description TEXT,
    impact_cost DECIMAL(15,2),
    impact_days INTEGER,
    impact_quality TEXT,
    status VARCHAR(20) DEFAULT 'DRAFT',
    submitted_by VARCHAR(32) REFERENCES user_account(user_id),
    submitted_at TIMESTAMP,
    customer_approved_by VARCHAR(100),
    customer_approved_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 8.2.6 结算相关

**结算表 (Settlement)**
```sql
CREATE TABLE settlement (
    settlement_id VARCHAR(32) PRIMARY KEY,
    tenant_id VARCHAR(32) REFERENCES tenant(tenant_id),
    contract_id VARCHAR(32) REFERENCES contract(contract_id),
    settlement_period VARCHAR(20) NOT NULL, -- '2025Q1'
    formula_version VARCHAR(20) NOT NULL,
    base_amount DECIMAL(15,2), -- 基础成果费
    adjustment_amount DECIMAL(15,2), -- 调整金额
    final_amount DECIMAL(15,2) NOT NULL, -- 最终成果费
    calculation_details JSONB, -- 计算明细
    data_health_score INTEGER,
    status VARCHAR(20) DEFAULT 'CALCULATING',
    confirmed_by_customer VARCHAR(100),
    confirmed_at TIMESTAMP,
    audit_package_url VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 8.3 索引设计

**性能关键索引：**
```sql
-- 租户隔离索引
CREATE INDEX idx_project_tenant_id ON project(tenant_id);
CREATE INDEX idx_kpi_fact_tenant_id ON kpi_fact(tenant_id);

-- 业务查询索引
CREATE INDEX idx_project_contract_id ON project(contract_id);
CREATE INDEX idx_kpi_fact_project_period ON kpi_fact(project_id, measure_period);
CREATE INDEX idx_cost_project_date ON project_cost(project_id, cost_date);

-- 复合索引
CREATE INDEX idx_kpi_fact_composite ON kpi_fact(tenant_id, kpi_id, project_id, measure_period);
CREATE INDEX idx_settlement_composite ON settlement(tenant_id, contract_id, settlement_period);
```

### 8.4 数据分区策略

**时间分区：**
```sql
-- KPI事实表按月分区
CREATE TABLE kpi_fact_2025_01 PARTITION OF kpi_fact
FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');

-- 成本表按季度分区
CREATE TABLE project_cost_2025_q1 PARTITION OF project_cost
FOR VALUES FROM ('2025-01-01') TO ('2025-04-01');
```

**租户分区：**
```sql
-- 大租户独立分区
CREATE TABLE kpi_fact_tenant_001 PARTITION OF kpi_fact
FOR VALUES IN ('tenant_001');
```

---

## 9. 接口与集成

### 9.1 API接口设计

#### 9.1.1 RESTful API规范

**基础规范：**
- 基础URL：`https://api.platform.com/v1`
- 认证方式：Bearer Token (JWT)
- 数据格式：JSON
- 字符编码：UTF-8
- 时间格式：ISO 8601 (2025-01-14T10:30:00Z)

**HTTP状态码：**
- 200 OK：请求成功
- 201 Created：资源创建成功
- 400 Bad Request：请求参数错误
- 401 Unauthorized：未授权
- 403 Forbidden：权限不足
- 404 Not Found：资源不存在
- 500 Internal Server Error：服务器内部错误

#### 9.1.2 核心API接口

**1. 认证接口**
```http
POST /auth/login
Content-Type: application/json

{
  "username": "<EMAIL>",
  "password": "password123",
  "tenant_id": "tenant_001"
}

Response:
{
  "access_token": "eyJhbGciOiJIUzI1NiIs...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIs...",
  "expires_in": 3600,
  "user_info": {
    "user_id": "user_001",
    "username": "<EMAIL>",
    "full_name": "张三",
    "roles": ["PM", "USER"]
  }
}
```

**2. 项目管理接口**
```http
GET /projects?status=ongoing&page=1&size=20
Authorization: Bearer {access_token}

Response:
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 150,
    "page": 1,
    "size": 20,
    "items": [
      {
        "project_id": "proj_001",
        "project_name": "智能制造系统集成项目",
        "project_status": "ONGOING",
        "budget_amount": 5000000.00,
        "actual_cost": 3200000.00,
        "progress": 65.5,
        "otd_rate": 92.3
      }
    ]
  }
}
```

**3. KPI计算接口**
```http
POST /kpis/compute
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "period": "2025Q1",
  "scope": ["proj_001", "proj_002"],
  "kpi_ids": ["kpi_margin", "kpi_otd"],
  "force_recalculate": false
}

Response:
{
  "code": 200,
  "message": "计算任务已提交",
  "data": {
    "task_id": "task_001",
    "status": "RUNNING",
    "estimated_duration": 300
  }
}
```

**4. 变更管理接口**
```http
POST /changes
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "project_id": "proj_001",
  "change_title": "增加数据接口模块",
  "change_type": "REQUIREMENT",
  "description": "客户要求增加与第三方系统的数据接口",
  "impact_cost": 150000.00,
  "impact_days": 15,
  "attachments": [
    {
      "file_name": "需求变更说明.pdf",
      "file_url": "https://storage.com/files/change_001.pdf"
    }
  ]
}

Response:
{
  "code": 201,
  "message": "变更单创建成功",
  "data": {
    "change_id": "chg_001",
    "change_no": "CHG-2025-001",
    "status": "DRAFT"
  }
}
```

**5. 结算接口**
```http
POST /settlements/close
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "period": "2025Q1",
  "contract_id": "cont_001",
  "formula_version": "v1.2"
}

Response:
{
  "code": 200,
  "message": "结算计算完成",
  "data": {
    "settlement_id": "settle_001",
    "final_amount": 125000.00,
    "calculation_details": {
      "base_performance_fee": 100000.00,
      "margin_improvement": 80000.00,
      "otd_improvement": 30000.00,
      "cash_benefit": 15000.00,
      "cap_adjustment": -25000.00
    }
  }
}
```

#### 9.1.3 Webhook事件

**事件类型：**
```javascript
// 基线冻结事件
{
  "event_type": "baseline.frozen",
  "timestamp": "2025-01-14T10:30:00Z",
  "data": {
    "baseline_id": "base_001",
    "kpi_id": "kpi_margin",
    "baseline_value": 15.5,
    "frozen_by": "user_001"
  }
}

// 变更审批事件
{
  "event_type": "change.approved",
  "timestamp": "2025-01-14T11:00:00Z",
  "data": {
    "change_id": "chg_001",
    "project_id": "proj_001",
    "approved_by": "customer_001",
    "impact_cost": 150000.00
  }
}

// 结算完成事件
{
  "event_type": "settlement.closed",
  "timestamp": "2025-01-14T12:00:00Z",
  "data": {
    "settlement_id": "settle_001",
    "contract_id": "cont_001",
    "period": "2025Q1",
    "final_amount": 125000.00
  }
}

// 数据健康告警事件
{
  "event_type": "data_health.alert",
  "timestamp": "2025-01-14T13:00:00Z",
  "data": {
    "project_id": "proj_001",
    "health_score": 75,
    "alert_level": "WARNING",
    "issues": ["数据延迟", "字段缺失"]
  }
}
```

### 9.2 ERP系统集成

#### 9.2.1 金蝶系统集成

**支持版本：**
- 金蝶K3 Cloud
- 金蝶云星空
- 金蝶KIS专业版

**集成方式：**
1. **官方API优先**：使用金蝶开放平台API
2. **数据库直连**：只读访问数据库视图
3. **中间表方式**：通过中间表交换数据
4. **文件导入**：Excel/CSV文件导入

**数据映射配置：**
```json
{
  "kingdee_k3_cloud": {
    "gl_account": {
      "source_table": "T_BD_Account",
      "fields": {
        "account_code": "FNumber",
        "account_name": "FName",
        "account_level": "FLevel",
        "is_leaf": "FIsLeaf"
      }
    },
    "ar_invoice": {
      "source_table": "T_AR_ReceiveBill",
      "fields": {
        "invoice_no": "FBillNo",
        "customer_code": "FCustomerID.FNumber",
        "invoice_amount": "FAmount",
        "invoice_date": "FDate"
      }
    },
    "purchase_order": {
      "source_table": "T_PUR_PurchaseOrderEntry",
      "fields": {
        "po_no": "FBillNo",
        "material_code": "FMaterialID.FNumber",
        "quantity": "FQty",
        "unit_price": "FPrice",
        "amount": "FAmount"
      }
    }
  }
}
```

#### 9.2.2 用友系统集成

**支持版本：**
- 用友U8+
- 用友NC Cloud
- 用友YonSuite

**集成配置：**
```json
{
  "yonyou_u8": {
    "connection": {
      "type": "database",
      "host": "*************",
      "port": 1433,
      "database": "UFDATA_001_2025",
      "username": "readonly_user",
      "password": "encrypted_password"
    },
    "sync_schedule": {
      "gl_data": "0 2 * * *",  // 每日2点同步总账数据
      "ar_data": "0 3 * * *",  // 每日3点同步应收数据
      "inventory": "0 4 * * *"  // 每日4点同步库存数据
    }
  }
}
```

### 9.3 文件导入模板

#### 9.3.1 成本数据导入模板

**Excel模板结构：**
| 列名 | 数据类型 | 必填 | 说明 | 示例 |
|------|----------|------|------|------|
| 项目编号 | 文本 | 是 | 系统中的项目编号 | PROJ-2025-001 |
| 成本类别 | 文本 | 是 | MATERIAL/LABOR/OVERHEAD | MATERIAL |
| 成本子类 | 文本 | 否 | 具体成本分类 | 原材料 |
| 成本金额 | 数值 | 是 | 成本金额，保留2位小数 | 15000.00 |
| 币种 | 文本 | 否 | 默认CNY | CNY |
| 发生日期 | 日期 | 是 | YYYY-MM-DD格式 | 2025-01-14 |
| 单据号 | 文本 | 否 | 原始单据编号 | PO-2025-001 |
| 供应商编号 | 文本 | 否 | 供应商编号 | SUP-001 |
| 备注 | 文本 | 否 | 其他说明 | 紧急采购 |

#### 9.3.2 里程碑数据导入模板

**CSV模板结构：**
```csv
项目编号,里程碑名称,里程碑类型,计划完成日期,实际完成日期,权重,状态,备注
PROJ-2025-001,需求确认,REQUIREMENT,2025-01-15,2025-01-14,0.1,COMPLETED,提前1天完成
PROJ-2025-001,设计评审,DESIGN,2025-02-15,,0.2,ONGOING,设计阶段进行中
PROJ-2025-001,采购完成,PROCUREMENT,2025-03-15,,0.3,PLANNED,等待设计完成
```

### 9.4 银行对账集成 (Phase 2)

#### 9.4.1 银企直联

**支持银行：**
- 工商银行：ICBC
- 建设银行：CCB
- 招商银行：CMB
- 平安银行：PAB

**对账流程：**
```mermaid
sequenceDiagram
    participant Bank as 银行系统
    participant Platform as 平台系统
    participant Finance as 财务人员

    Bank->>Platform: 每日对账单推送
    Platform->>Platform: 解析对账单数据
    Platform->>Platform: 自动匹配应收款
    Platform->>Finance: 未匹配项目提醒
    Finance->>Platform: 手工匹配确认
    Platform->>Platform: 更新回款状态
```

#### 9.4.2 电子发票集成

**发票平台对接：**
- 税务局电子发票平台
- 第三方发票服务商
- 企业自建发票系统

**开票流程：**
```javascript
// 开票申请
{
  "invoice_request": {
    "buyer_info": {
      "company_name": "客户公司名称",
      "tax_number": "91110000000000000X",
      "address": "北京市朝阳区xxx",
      "phone": "010-********",
      "bank_account": "**********"
    },
    "items": [
      {
        "item_name": "技术服务费",
        "specification": "项目制按结果买单服务",
        "unit": "项",
        "quantity": 1,
        "unit_price": 125000.00,
        "amount": 125000.00,
        "tax_rate": 0.06
      }
    ],
    "total_amount": 125000.00,
    "tax_amount": 7500.00,
    "remark": "2025Q1绩效成果费"
  }
}
```

---

## 10. UI与交互设计

### 10.1 设计原则

**用户体验原则：**
- **简洁明了**：界面简洁，信息层次清晰
- **一致性**：统一的设计语言和交互模式
- **响应式**：适配不同屏幕尺寸和设备
- **可访问性**：支持键盘导航和屏幕阅读器

**业务导向原则：**
- **角色导向**：不同角色看到不同的界面和功能
- **流程导向**：界面设计贴合业务流程
- **数据驱动**：重要数据突出显示
- **决策支持**：提供决策所需的关键信息

### 10.2 关键页面设计

#### 10.2.1 首页仪表盘

**布局结构：**
```
+------------------+------------------+------------------+
|    KPI概览卡片    |    KPI概览卡片    |    KPI概览卡片    |
|   毛利率 15.2%   |   OTD率 92.3%   |   DSO 45天      |
+------------------+------------------+------------------+
|                    KPI趋势图表                        |
|  [毛利率] [OTD率] [DSO] [成本偏差] 时间范围选择        |
+-------------------------------------------------------+
|  待处理事项        |  数据健康状态      |  最新动态        |
|  □ 3个变更待审批   |  ● 项目A: 85分    |  ○ 基线已冻结     |
|  □ 2个对账待确认   |  ● 项目B: 92分    |  ○ 变更已审批     |
|  □ 1个重基线申请   |  ● 项目C: 78分    |  ○ 结算已完成     |
+-------------------------------------------------------+
```

**功能特性：**
- 实时数据更新（每5分钟刷新）
- 可配置的KPI卡片
- 交互式图表（点击查看详情）
- 智能告警提醒
- 快速操作入口

#### 10.2.2 项目详情页

**页面结构：**
```
项目名称: 智能制造系统集成项目                    [编辑] [导出]

+-- 基本信息 --+-- 财务概览 --+-- 进度状态 --+-- 团队信息 --+
|  项目编号     |  预算金额     |  总体进度     |  项目经理     |
|  客户名称     |  实际成本     |  里程碑状态   |  核心成员     |
|  合同金额     |  毛利率       |  风险等级     |  外部顾问     |
+-------------+-------------+-------------+-------------+

+-- KPI仪表盘 --+
|  [毛利率] [OTD率] [成本偏差] [质量指标]           |
|  ○ 15.2% ↑  ○ 92.3% ↓  ○ -2.1% ↑  ○ 98.5% →  |
+-----------------------------------------------+

+-- 详细信息标签页 --+
|  [里程碑] [成本明细] [变更记录] [质量数据] [文档]  |
+-----------------------------------------------+
```

**交互特性：**
- 标签页切换
- 数据钻取（点击KPI查看明细）
- 实时数据更新
- 批量操作支持
- 导出功能

#### 10.2.3 KPI配置中心

**配置界面：**
```
KPI配置中心                                    [新建KPI]

+-- KPI列表 --+
| 搜索: [________________] [筛选▼] [排序▼]      |
|                                              |
| ○ 毛利率 (kpi_margin)           [编辑] [删除] |
|   公式: (收入-成本)/收入*100                   |
|   状态: 启用  版本: v1.2                     |
|                                              |
| ○ OTD准时交付率 (kpi_otd)       [编辑] [删除] |
|   公式: 准时完成数/总里程碑数*100              |
|   状态: 启用  版本: v1.1                     |
+--------------------------------------------+

+-- 公式编辑器 --+
| KPI名称: [毛利率_________________________]    |
| KPI代码: [kpi_margin___________________]      |
| 分类: [财务指标▼]  单位: [%___]              |
|                                              |
| 计算公式:                                     |
| +------------------------------------------+ |
| | (sum(Revenue) - sum(Cost)) / sum(Revenue) | |
| | * 100                                    | |
| +------------------------------------------+ |
| [语法检查] [模拟计算] [保存] [取消]            |
+--------------------------------------------+
```

#### 10.2.4 变更管理页面

**变更单列表：**
```
变更管理                                      [新建变更]

筛选条件: 项目[全部▼] 状态[全部▼] 类型[全部▼] 时间[本月▼]

+-- 变更单列表 --+
| 变更编号 | 项目名称 | 变更类型 | 影响成本 | 状态 | 操作 |
|----------|----------|----------|----------|------|------|
| CHG-001  | 项目A    | 需求变更 | +15万    | 待审批| [查看]|
| CHG-002  | 项目B    | 设计变更 | +8万     | 已确认| [查看]|
| CHG-003  | 项目C    | 技术变更 | -2万     | 执行中| [查看]|
+-----------------------------------------------+

+-- 变更详情 --+
| 变更标题: 增加数据接口模块                      |
| 变更类型: 需求变更    来源: 客户要求            |
| 项目: 智能制造系统集成项目                      |
|                                              |
| 变更描述:                                     |
| 客户要求增加与第三方ERP系统的数据接口...        |
|                                              |
| 影响评估:                                     |
| • 成本影响: +150,000元                       |
| • 工期影响: +15天                            |
| • 质量影响: 增加集成测试工作量                 |
|                                              |
| 审批流程:                                     |
| ✓ 技术评审 (张工程师, 2025-01-10)             |
| ○ 商务审批 (李经理, 待处理)                   |
| ○ 客户确认 (待商务审批完成)                   |
|                                              |
| [批准] [拒绝] [退回修改] [导出PDF]             |
+--------------------------------------------+
```

#### 10.2.5 结算中心

**结算界面：**
```
结算中心 - 2025Q1季度结算                      [导出对账单]

+-- 结算概览 --+
| 合同: 智能制造系统集成项目合同                  |
| 结算周期: 2025Q1 (2025-01-01 至 2025-03-31)  |
| 公式版本: v1.2                               |
| 数据健康评分: 92分 ✓                         |
+--------------------------------------------+

+-- KPI改进情况 --+
| 指标名称    | 基线值  | 当前值  | 改进幅度 | 改进金额  |
|------------|---------|---------|----------|-----------|
| 毛利率      | 15.0%   | 17.2%   | +2.2%    | +110,000  |
| OTD率       | 85.0%   | 92.3%   | +7.3%    | +36,500   |
| DSO天数     | 60天    | 45天    | -15天    | +25,000   |
| 返工率      | 3.2%    | 2.1%    | -1.1%    | +15,500   |
+-------------------------------------------------------+

+-- 成果费计算 --+
| 计算项目              | 金额      | 比例  | 成果费    |
|---------------------|-----------|-------|-----------|
| 毛利改进 (110,000)   | 110,000   | 15%   | 16,500    |
| 交期改进 (36,500)    | 36,500    | 20%   | 7,300     |
| 现金改进 (25,000)    | 25,000    | 30%   | 7,500     |
| 质量改进 (15,500)    | 15,500    | 10%   | 1,550     |
|---------------------|-----------|-------|-----------|
| 小计                |           |       | 32,850    |
| 封顶调整 (2.5倍订阅费)|          |       | -7,850    |
| 最终成果费           |           |       | 25,000    |
+-------------------------------------------------------+

+-- 操作区域 --+
| [生成对账单] [发送客户] [确认结算] [查看明细]    |
+--------------------------------------------+
```

### 10.3 移动端设计 (Phase 2)

#### 10.3.1 响应式设计

**断点设置：**
- 手机：< 768px
- 平板：768px - 1024px
- 桌面：> 1024px

**移动端优化：**
- 触摸友好的按钮尺寸
- 简化的导航结构
- 关键信息优先显示
- 手势操作支持

#### 10.3.2 工时录入App

**功能特性：**
- 项目选择和工时录入
- 离线数据缓存
- 照片和语音备注
- GPS位置记录
- 数据同步

---

## 11. 流程与状态机

### 11.1 变更单状态机

```mermaid
stateDiagram-v2
    [*] --> 草稿
    草稿 --> 待评估: 提交评估
    草稿 --> 已作废: 作废

    待评估 --> 评估完成: 完成评估
    待评估 --> 草稿: 退回修改
    待评估 --> 已作废: 作废

    评估完成 --> 待客户确认: 提交客户
    评估完成 --> 待评估: 重新评估

    待客户确认 --> 已确认: 客户同意
    待客户确认 --> 已拒绝: 客户拒绝
    待客户确认 --> 评估完成: 客户要求修改

    已确认 --> 执行中: 开始执行
    执行中 --> 已完成: 执行完成
    已完成 --> 已关闭: 影响确认

    已拒绝 --> [*]
    已作废 --> [*]
    已关闭 --> [*]
```

**状态说明：**
- **草稿**：变更单创建但未提交
- **待评估**：等待技术和商务评估
- **评估完成**：内部评估完成，等待提交客户
- **待客户确认**：等待客户确认变更方案
- **已确认**：客户确认，准备执行
- **执行中**：变更正在执行
- **已完成**：变更执行完成
- **已关闭**：变更影响已确认，流程结束
- **已拒绝**：客户拒绝变更
- **已作废**：变更单作废

### 11.2 基线状态机

```mermaid
stateDiagram-v2
    [*] --> 草稿
    草稿 --> 待审批: 提交审批
    草稿 --> 已删除: 删除

    待审批 --> 已冻结: 审批通过
    待审批 --> 草稿: 审批拒绝

    已冻结 --> 待重基线: 触发重基线
    已冻结 --> 已失效: 基线失效

    待重基线 --> 已重基线: 重基线完成
    待重基线 --> 已冻结: 取消重基线

    已重基线 --> 待重基线: 再次重基线
    已重基线 --> 已失效: 基线失效

    已删除 --> [*]
    已失效 --> [*]
```

### 11.3 结算状态机

```mermaid
stateDiagram-v2
    [*] --> 计算中
    计算中 --> 计算完成: 计算成功
    计算中 --> 计算失败: 计算错误

    计算失败 --> 计算中: 重新计算
    计算失败 --> 已取消: 取消结算

    计算完成 --> 待复核: 数据健康分<80
    计算完成 --> 待对账: 数据健康分≥80

    待复核 --> 待对账: 数据修复完成
    待复核 --> 已取消: 取消结算

    待对账 --> 已确认: 双方确认
    待对账 --> 有争议: 存在争议
    待对账 --> 计算中: 重新计算

    有争议 --> 已确认: 争议解决
    有争议 --> 已取消: 取消结算

    已确认 --> 已开票: 开具发票
    已开票 --> 已回款: 收到回款
    已回款 --> 已关闭: 结算完成

    已取消 --> [*]
    已关闭 --> [*]
```

### 11.4 SLA时限设置

**变更管理SLA：**
- 技术评估：2个工作日
- 商务审批：1个工作日
- 客户确认：5个工作日
- 变更执行：按变更复杂度确定

**结算管理SLA：**
- 数据准备：结算期结束后3个工作日
- KPI计算：1个工作日
- 对账确认：5个工作日
- 开票处理：2个工作日

**基线管理SLA：**
- 基线计算：1个工作日
- 内部审批：3个工作日
- 客户确认：7个工作日
- 基线冻结：即时生效

---

## 12. 权限矩阵

### 12.1 角色权限详细定义

#### 12.1.1 系统管理员 (Admin)

| 功能模块 | 权限范围 | 具体权限 |
|----------|----------|----------|
| 系统配置 | 全部 | 租户管理、组织架构、系统参数配置 |
| 用户管理 | 全部 | 用户创建、角色分配、权限管理 |
| 数据源管理 | 全部 | 数据源配置、映射设置、同步管理 |
| KPI配置 | 全部 | KPI定义、公式编辑、模板管理 |
| 基线管理 | 全部 | 基线计算、审批、冻结 |
| 审计日志 | 全部 | 查看所有操作日志、导出审计包 |

#### 12.1.2 财务总监 (CFO)

| 功能模块 | 权限范围 | 具体权限 |
|----------|----------|----------|
| 财务数据 | 全部 | 查看所有项目财务数据 |
| 成本核算 | 全部 | 成本分析、毛利计算、PPV分析 |
| 结算管理 | 全部 | 结算审批、对账确认、开票管理 |
| 报表导出 | 全部 | 导出所有财务报表和明细 |
| 基线审批 | 全部 | 基线冻结审批、重基线审批 |
| 变更审批 | 财务相关 | 涉及成本变更的审批权限 |

#### 12.1.3 项目经理 (PM)

| 功能模块 | 权限范围 | 具体权限 |
|----------|----------|----------|
| 项目管理 | 负责项目 | 项目信息维护、里程碑管理 |
| 变更管理 | 负责项目 | 变更申请、影响评估、执行跟踪 |
| 成本查看 | 负责项目 | 查看项目成本明细和趋势 |
| KPI查看 | 负责项目 | 查看项目KPI和改进情况 |
| 团队管理 | 负责项目 | 项目团队成员管理 |
| 报表导出 | 负责项目 | 导出项目相关报表 |

#### 12.1.4 采购经理 (SCM)

| 功能模块 | 权限范围 | 具体权限 |
|----------|----------|----------|
| 采购数据 | 相关项目 | 采购订单、入库单、发票录入 |
| 供应商管理 | 全部 | 供应商信息维护、评估 |
| PPV分析 | 相关项目 | 采购价格差异分析 |
| 成本录入 | 采购相关 | 材料成本、外协成本录入 |
| 报表查看 | 采购相关 | 采购相关报表查看 |

#### 12.1.5 质量工程师 (QA)

| 功能模块 | 权限范围 | 具体权限 |
|----------|----------|----------|
| 质量数据 | 相关项目 | 质量检测数据、返工记录录入 |
| 质量分析 | 相关项目 | 质量KPI分析、改进建议 |
| 变更评估 | 技术相关 | 技术变更的质量影响评估 |
| 报表查看 | 质量相关 | 质量相关报表查看 |

#### 12.1.6 销售经理 (Sales)

| 功能模块 | 权限范围 | 具体权限 |
|----------|----------|----------|
| 客户管理 | 负责客户 | 客户信息维护、关系管理 |
| 合同管理 | 负责合同 | 合同信息维护、条款管理 |
| 收入确认 | 负责项目 | 项目收入确认、开票申请 |
| 客户沟通 | 负责客户 | 变更确认、对账沟通 |
| 报表查看 | 负责项目 | 销售相关报表查看 |

#### 12.1.7 审计员 (Auditor)

| 功能模块 | 权限范围 | 具体权限 |
|----------|----------|----------|
| 数据查看 | 全部 | 只读访问所有业务数据 |
| 审计日志 | 全部 | 查看所有操作和变更日志 |
| 报表导出 | 全部 | 导出审计所需的所有报表 |
| 审计包 | 全部 | 下载完整审计数据包 |
| 合规检查 | 全部 | 合规性检查和风险评估 |

### 12.2 数据权限控制

#### 12.2.1 行级权限 (Row Level Security)

**项目数据权限：**
```sql
-- 项目经理只能看到自己负责的项目
CREATE POLICY pm_project_policy ON project
FOR ALL TO project_manager
USING (project_manager_id = current_user_id());

-- 销售经理只能看到自己负责的客户项目
CREATE POLICY sales_project_policy ON project
FOR ALL TO sales_manager
USING (contract_id IN (
  SELECT contract_id FROM contract
  WHERE sales_manager_id = current_user_id()
));
```

**财务数据权限：**
```sql
-- 财务人员可以看到所有财务数据
CREATE POLICY finance_cost_policy ON project_cost
FOR ALL TO finance_user
USING (true);

-- 项目经理只能看到自己项目的成本
CREATE POLICY pm_cost_policy ON project_cost
FOR ALL TO project_manager
USING (project_id IN (
  SELECT project_id FROM project
  WHERE project_manager_id = current_user_id()
));
```

#### 12.2.2 字段级权限 (Column Level Security)

**敏感字段控制：**
```sql
-- 成本金额字段权限控制
GRANT SELECT (cost_amount) ON project_cost TO finance_user;
GRANT SELECT (cost_amount) ON project_cost TO project_manager;
REVOKE SELECT (cost_amount) ON project_cost FROM quality_engineer;

-- 利润数据字段权限控制
GRANT SELECT (margin_amount) ON project_revenue TO cfo;
GRANT SELECT (margin_amount) ON project_revenue TO finance_manager;
REVOKE SELECT (margin_amount) ON project_revenue FROM project_manager;
```

### 12.3 功能权限矩阵

| 功能 | Admin | CFO | PM | SCM | QA | Sales | Auditor |
|------|-------|-----|----|----|----|----|---------|
| 用户管理 | ✓ | ✗ | ✗ | ✗ | ✗ | ✗ | ✗ |
| 系统配置 | ✓ | ✗ | ✗ | ✗ | ✗ | ✗ | ✗ |
| KPI配置 | ✓ | ✓ | ✗ | ✗ | ✗ | ✗ | R |
| 基线管理 | ✓ | ✓ | R | R | R | R | R |
| 项目管理 | ✓ | R | ✓ | R | R | ✓ | R |
| 变更管理 | ✓ | A | ✓ | R | ✓ | ✓ | R |
| 成本录入 | ✓ | ✓ | R | ✓ | ✓ | ✗ | R |
| 收入确认 | ✓ | ✓ | R | ✗ | ✗ | ✓ | R |
| 结算管理 | ✓ | ✓ | R | R | R | R | R |
| 报表导出 | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ | ✓ |
| 审计日志 | ✓ | ✓ | ✗ | ✗ | ✗ | ✗ | ✓ |

**权限说明：**
- ✓ = 完全权限 (Create, Read, Update, Delete)
- R = 只读权限 (Read Only)
- A = 审批权限 (Approve)
- ✗ = 无权限 (No Access)

### 12.4 客户协同权限

#### 12.4.1 客户门户权限

**客户用户权限：**
- 查看指定项目的基本信息
- 查看项目进度和里程碑状态
- 查看变更申请和影响评估
- 确认变更方案和费用
- 查看对账单和结算明细
- 电子签名和确认

**权限限制：**
- 无法查看详细成本构成
- 无法查看供应商信息
- 无法查看内部工时数据
- 无法修改任何基础数据

#### 12.4.2 API权限控制

**API访问控制：**
```javascript
// JWT Token中包含权限信息
{
  "user_id": "user_001",
  "tenant_id": "tenant_001",
  "roles": ["project_manager"],
  "permissions": [
    "project:read:own",
    "change:create:own",
    "cost:read:own"
  ],
  "data_scope": {
    "projects": ["proj_001", "proj_002"],
    "customers": ["cust_001"]
  }
}

// API接口权限验证
@RequirePermission("project:read:own")
@DataScope(type="project", field="project_id")
public ProjectResponse getProject(String projectId) {
    // 业务逻辑
}
```

---

## 13. 异常与边界处理

### 13.1 数据异常处理

#### 13.1.1 数据缺失处理

**缺失数据分类：**
1. **关键数据缺失**：影响KPI计算的核心数据
2. **一般数据缺失**：不影响核心计算的辅助数据
3. **历史数据缺失**：基线计算所需的历史数据

**处理策略：**
```javascript
// 数据缺失处理规则
const missingDataRules = {
  // 关键数据缺失 - 阻止计算
  critical: {
    fields: ['project_cost', 'project_revenue', 'milestone_date'],
    action: 'BLOCK_CALCULATION',
    notification: 'IMMEDIATE_ALERT'
  },

  // 一般数据缺失 - 使用默认值
  normal: {
    fields: ['supplier_info', 'cost_category'],
    action: 'USE_DEFAULT',
    notification: 'DAILY_REPORT'
  },

  // 历史数据缺失 - 调整基线窗口
  historical: {
    fields: ['baseline_data'],
    action: 'ADJUST_BASELINE_WINDOW',
    notification: 'WEEKLY_REPORT'
  }
};
```

**补录机制：**
- 自动提醒相关人员补录
- 提供便捷的数据补录界面
- 支持批量数据导入
- 补录后自动重新计算

#### 13.1.2 数据延迟处理

**延迟等级定义：**
- **轻微延迟**：1-2天，黄色预警
- **中度延迟**：3-5天，橙色告警
- **严重延迟**：>5天，红色告警，限制结算

**处理机制：**
```javascript
// 延迟数据处理
function handleDelayedData(dataType, delayDays) {
  if (delayDays <= 2) {
    // 轻微延迟 - 继续计算，标记风险
    return {
      action: 'CONTINUE_WITH_WARNING',
      riskLevel: 'LOW',
      message: '数据轻微延迟，请及时更新'
    };
  } else if (delayDays <= 5) {
    // 中度延迟 - 降低数据健康分
    return {
      action: 'REDUCE_HEALTH_SCORE',
      riskLevel: 'MEDIUM',
      healthPenalty: 10,
      message: '数据延迟较多，影响计算准确性'
    };
  } else {
    // 严重延迟 - 限制结算
    return {
      action: 'BLOCK_SETTLEMENT',
      riskLevel: 'HIGH',
      message: '数据严重延迟，无法进行结算'
    };
  }
}
```

#### 13.1.3 数据异常检测

**异常检测算法：**
```python
# 基于统计的异常检测
def detect_statistical_anomaly(data, threshold=3):
    """
    使用3σ原则检测异常值
    """
    mean = np.mean(data)
    std = np.std(data)

    anomalies = []
    for i, value in enumerate(data):
        z_score = abs((value - mean) / std)
        if z_score > threshold:
            anomalies.append({
                'index': i,
                'value': value,
                'z_score': z_score,
                'type': 'STATISTICAL_OUTLIER'
            })

    return anomalies

# 基于业务规则的异常检测
def detect_business_anomaly(cost_data):
    """
    基于业务规则检测异常
    """
    anomalies = []

    for record in cost_data:
        # 成本金额异常
        if record['amount'] < 0:
            anomalies.append({
                'record_id': record['id'],
                'type': 'NEGATIVE_COST',
                'message': '成本金额不能为负数'
            })

        # 单笔金额过大
        if record['amount'] > 1000000:
            anomalies.append({
                'record_id': record['id'],
                'type': 'EXCESSIVE_AMOUNT',
                'message': '单笔成本金额异常过大'
            })

        # 日期异常
        if record['cost_date'] > datetime.now():
            anomalies.append({
                'record_id': record['id'],
                'type': 'FUTURE_DATE',
                'message': '成本日期不能是未来日期'
            })

    return anomalies
```

### 13.2 业务边界处理

#### 13.2.1 负绩效保护

**负绩效场景：**
- 项目毛利率下降
- OTD率恶化
- 成本超支严重
- 质量问题增加

**保护机制：**
```javascript
// 负绩效保护算法
function applyNegativeProtection(performanceFee) {
  // 基础保护：成果费不低于0
  const protectedFee = Math.max(performanceFee, 0);

  // 记录负绩效情况用于分析
  if (performanceFee < 0) {
    logNegativePerformance({
      originalFee: performanceFee,
      protectedFee: protectedFee,
      reason: '负向保护机制生效',
      timestamp: new Date()
    });
  }

  return {
    finalFee: protectedFee,
    isProtected: performanceFee < 0,
    originalFee: performanceFee
  };
}
```

#### 13.2.2 多币种处理

**汇率风险处理：**
```javascript
// 汇率波动处理
const exchangeRateRules = {
  // 汇率波动阈值
  volatilityThreshold: 0.05, // 5%

  // 汇率来源优先级
  rateSources: [
    'CENTRAL_BANK',    // 央行汇率
    'COMMERCIAL_BANK', // 商业银行汇率
    'MARKET_RATE'      // 市场汇率
  ],

  // 汇率固定策略
  fixingStrategy: {
    // 合同签订时固定汇率
    contractSigning: true,
    // 结算时固定汇率
    settlementPeriod: false,
    // 月末汇率
    monthEnd: true
  }
};

// 汇率影响计算
function calculateExchangeRateImpact(amount, fromCurrency, toCurrency, baselineRate, currentRate) {
  const baselineAmount = amount * baselineRate;
  const currentAmount = amount * currentRate;
  const impact = currentAmount - baselineAmount;

  // 判断是否超过阈值
  const impactRatio = Math.abs(impact) / baselineAmount;
  const isControllable = impactRatio <= exchangeRateRules.volatilityThreshold;

  return {
    impact: impact,
    impactRatio: impactRatio,
    isControllable: isControllable,
    adjustedAmount: isControllable ? currentAmount : baselineAmount
  };
}
```

#### 13.2.3 大宗商品价格波动

**价格指数监控：**
```javascript
// 大宗商品价格监控
const commodityPriceMonitor = {
  // 监控的商品类别
  categories: [
    'STEEL',      // 钢材
    'COPPER',     // 铜
    'ALUMINUM',   // 铝
    'PLASTIC',    // 塑料
    'ELECTRONICS' // 电子元器件
  ],

  // 价格波动阈值
  thresholds: {
    'STEEL': 0.08,      // 8%
    'COPPER': 0.10,     // 10%
    'ALUMINUM': 0.08,   // 8%
    'PLASTIC': 0.12,    // 12%
    'ELECTRONICS': 0.15 // 15%
  },

  // 价格指数来源
  indexSources: [
    'SHANGHAI_FUTURES_EXCHANGE',
    'LONDON_METAL_EXCHANGE',
    'INDUSTRY_ASSOCIATION'
  ]
};

// 价格波动影响评估
function assessPriceVolatilityImpact(material, baselinePrice, currentPrice) {
  const priceChange = (currentPrice - baselinePrice) / baselinePrice;
  const threshold = commodityPriceMonitor.thresholds[material.category];

  if (Math.abs(priceChange) > threshold) {
    // 超过阈值，触发重基线
    return {
      action: 'TRIGGER_REBASELINE',
      priceChange: priceChange,
      threshold: threshold,
      isControllable: false,
      reason: `${material.category}价格波动超过${threshold * 100}%阈值`
    };
  } else {
    // 在阈值内，视为可控
    return {
      action: 'INCLUDE_IN_PERFORMANCE',
      priceChange: priceChange,
      threshold: threshold,
      isControllable: true
    };
  }
}
```

### 13.3 系统异常处理

#### 13.3.1 计算引擎异常

**异常类型：**
- 公式语法错误
- 数据类型不匹配
- 除零错误
- 内存溢出
- 超时异常

**处理机制：**
```javascript
// 计算引擎异常处理
class CalculationEngine {
  async calculateKPI(formula, data) {
    try {
      // 预检查
      this.validateFormula(formula);
      this.validateData(data);

      // 执行计算
      const result = await this.executeCalculation(formula, data);

      // 结果验证
      this.validateResult(result);

      return {
        success: true,
        result: result,
        timestamp: new Date()
      };

    } catch (error) {
      // 异常处理
      return this.handleCalculationError(error, formula, data);
    }
  }

  handleCalculationError(error, formula, data) {
    const errorInfo = {
      success: false,
      error: error.message,
      errorType: error.constructor.name,
      formula: formula,
      dataSize: data.length,
      timestamp: new Date()
    };

    // 记录错误日志
    this.logError(errorInfo);

    // 发送告警
    this.sendAlert(errorInfo);

    // 尝试降级处理
    if (error instanceof TimeoutError) {
      return this.fallbackCalculation(formula, data);
    }

    return errorInfo;
  }
}
```

#### 13.3.2 数据同步异常

**同步异常处理：**
```javascript
// 数据同步异常处理
class DataSyncService {
  async syncData(dataSource) {
    const maxRetries = 3;
    let retryCount = 0;

    while (retryCount < maxRetries) {
      try {
        const result = await this.performSync(dataSource);

        // 同步成功，更新状态
        await this.updateSyncStatus(dataSource.id, 'SUCCESS', result);
        return result;

      } catch (error) {
        retryCount++;

        // 记录重试日志
        this.logRetry(dataSource.id, retryCount, error);

        if (retryCount >= maxRetries) {
          // 达到最大重试次数，标记失败
          await this.updateSyncStatus(dataSource.id, 'FAILED', error);

          // 发送告警
          this.sendSyncFailureAlert(dataSource, error);

          throw error;
        }

        // 等待后重试
        await this.delay(Math.pow(2, retryCount) * 1000); // 指数退避
      }
    }
  }
}
```

### 13.4 用户操作异常

#### 13.4.1 并发操作冲突

**乐观锁机制：**
```sql
-- 使用版本号实现乐观锁
UPDATE project SET
  project_name = ?,
  budget_amount = ?,
  version = version + 1,
  updated_at = CURRENT_TIMESTAMP
WHERE project_id = ? AND version = ?;

-- 检查更新结果
IF @@ROWCOUNT = 0 THEN
  THROW 'CONCURRENT_MODIFICATION_ERROR', '数据已被其他用户修改，请刷新后重试';
END IF;
```

#### 13.4.2 权限边界处理

**权限检查机制：**
```javascript
// 权限检查装饰器
function requirePermission(permission) {
  return function(target, propertyName, descriptor) {
    const method = descriptor.value;

    descriptor.value = async function(...args) {
      // 检查用户权限
      const hasPermission = await this.checkPermission(
        this.currentUser,
        permission,
        args[0] // 资源ID
      );

      if (!hasPermission) {
        throw new PermissionDeniedError(
          `用户 ${this.currentUser.username} 没有权限执行 ${permission}`
        );
      }

      // 执行原方法
      return method.apply(this, args);
    };

    return descriptor;
  };
}

// 使用示例
class ProjectService {
  @requirePermission('project:update')
  async updateProject(projectId, updateData) {
    // 业务逻辑
  }
}

---

## 14. 技术架构

### 14.1 总体架构设计

#### 14.1.1 微服务架构

```mermaid
graph TB
    subgraph "前端层"
        Web[Web应用]
        Mobile[移动应用]
        API[API网关]
    end

    subgraph "业务服务层"
        Auth[认证服务]
        Project[项目服务]
        KPI[KPI服务]
        Settlement[结算服务]
        Integration[集成服务]
        Notification[通知服务]
    end

    subgraph "数据层"
        PostgreSQL[(PostgreSQL)]
        ClickHouse[(ClickHouse)]
        Redis[(Redis)]
        MinIO[(MinIO)]
    end

    subgraph "基础设施"
        K8s[Kubernetes]
        Monitor[监控系统]
        Log[日志系统]
    end

    Web --> API
    Mobile --> API
    API --> Auth
    API --> Project
    API --> KPI
    API --> Settlement
    API --> Integration
    API --> Notification

    Project --> PostgreSQL
    KPI --> ClickHouse
    Settlement --> PostgreSQL
    Integration --> Redis

    Auth --> Redis
    Notification --> Redis

    Project --> MinIO
    Settlement --> MinIO
```

#### 14.1.2 技术栈选择

**前端技术栈：**
- **框架**：React 18 + TypeScript
- **UI库**：Ant Design 5.x
- **状态管理**：Redux Toolkit + RTK Query
- **图表库**：ECharts 5.x
- **构建工具**：Vite 4.x
- **移动端**：React Native (Phase 2)

**后端技术栈：**
- **语言**：Java 17 + Spring Boot 3.x
- **微服务**：Spring Cloud 2023.x
- **API网关**：Spring Cloud Gateway
- **服务注册**：Nacos
- **配置中心**：Nacos Config
- **消息队列**：Apache Kafka
- **缓存**：Redis 7.x
- **搜索引擎**：Elasticsearch 8.x

**数据存储：**
- **主数据库**：PostgreSQL 15
- **分析数据库**：ClickHouse 23.x
- **对象存储**：MinIO
- **时序数据**：InfluxDB (监控数据)

**基础设施：**
- **容器化**：Docker + Kubernetes
- **服务网格**：Istio (可选)
- **监控**：Prometheus + Grafana
- **日志**：ELK Stack (Elasticsearch + Logstash + Kibana)
- **链路追踪**：Jaeger

### 14.2 数据架构设计

#### 14.2.1 数据分层架构

```
数据架构分层
├── 数据接入层 (Data Ingestion)
│   ├── 实时数据流 (Kafka)
│   ├── 批量数据 (ETL)
│   └── API数据 (REST/GraphQL)
├── 数据存储层 (Data Storage)
│   ├── 事务数据 (PostgreSQL)
│   ├── 分析数据 (ClickHouse)
│   ├── 缓存数据 (Redis)
│   └── 文件数据 (MinIO)
├── 数据处理层 (Data Processing)
│   ├── 流处理 (Kafka Streams)
│   ├── 批处理 (Spring Batch)
│   └── 计算引擎 (自研DSL)
└── 数据服务层 (Data Service)
    ├── 数据API (REST)
    ├── 实时查询 (GraphQL)
    └── 报表服务 (Reporting)
```

#### 14.2.2 数据流设计

**实时数据流：**
```mermaid
graph LR
    ERP[ERP系统] --> Kafka[Kafka消息队列]
    Kafka --> Stream[流处理服务]
    Stream --> PG[(PostgreSQL)]
    Stream --> CH[(ClickHouse)]
    Stream --> Redis[(Redis缓存)]

    PG --> API[业务API]
    CH --> Analytics[分析API]
    Redis --> Cache[缓存API]
```

**批量数据流：**
```mermaid
graph LR
    File[文件数据] --> ETL[ETL服务]
    DB[外部数据库] --> ETL
    ETL --> Validate[数据验证]
    Validate --> Transform[数据转换]
    Transform --> Load[数据加载]
    Load --> PG[(PostgreSQL)]
    Load --> CH[(ClickHouse)]
```

### 14.3 安全架构设计

#### 14.3.1 安全防护体系

```mermaid
graph TB
    subgraph "网络安全"
        WAF[Web应用防火墙]
        LB[负载均衡器]
        VPN[VPN网关]
    end

    subgraph "应用安全"
        Auth[身份认证]
        AuthZ[权限控制]
        Encrypt[数据加密]
        Audit[审计日志]
    end

    subgraph "数据安全"
        Backup[数据备份]
        Mask[数据脱敏]
        DLP[数据防泄漏]
    end

    subgraph "基础设施安全"
        K8sSec[K8s安全]
        SecScan[安全扫描]
        Monitor[安全监控]
    end

    Internet --> WAF
    WAF --> LB
    LB --> Auth
    Auth --> AuthZ
    AuthZ --> Encrypt
    Encrypt --> Audit
```

#### 14.3.2 数据加密策略

**传输加密：**
- TLS 1.3 for HTTPS
- mTLS for 服务间通信
- VPN for 专线连接

**存储加密：**
```sql
-- 敏感字段加密
CREATE TABLE customer (
    customer_id VARCHAR(32) PRIMARY KEY,
    customer_name VARCHAR(200),
    -- 税号加密存储
    tax_number_encrypted BYTEA,
    -- 银行账号加密存储
    bank_account_encrypted BYTEA,
    -- 加密密钥ID
    encryption_key_id VARCHAR(32)
);

-- 加密函数
CREATE OR REPLACE FUNCTION encrypt_sensitive_data(
    plain_text TEXT,
    key_id VARCHAR(32)
) RETURNS BYTEA AS $$
BEGIN
    -- 使用AES-256-GCM加密
    RETURN pgp_sym_encrypt(plain_text, get_encryption_key(key_id));
END;
$$ LANGUAGE plpgsql;
```

### 14.4 性能优化设计

#### 14.4.1 缓存策略

**多级缓存架构：**
```
浏览器缓存 (1分钟)
    ↓
CDN缓存 (5分钟)
    ↓
API网关缓存 (30秒)
    ↓
应用缓存 (Redis, 10分钟)
    ↓
数据库查询缓存
```

**缓存配置：**
```yaml
# Redis缓存配置
cache:
  redis:
    host: redis-cluster
    port: 6379
    database: 0
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5

  # 缓存策略配置
  strategies:
    kpi-data:
      ttl: 600s  # 10分钟
      refresh-ahead: 60s
    user-session:
      ttl: 3600s  # 1小时
    project-info:
      ttl: 300s   # 5分钟
```

#### 14.4.2 数据库优化

**读写分离：**
```yaml
# 数据源配置
datasource:
  master:
    url: *****************************************
    username: app_user
    password: ${DB_PASSWORD}
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5

  slave:
    url: ****************************************
    username: readonly_user
    password: ${DB_READONLY_PASSWORD}
    hikari:
      maximum-pool-size: 15
      minimum-idle: 3
```

**分库分表策略：**
```sql
-- 按租户分库
CREATE DATABASE platform_tenant_001;
CREATE DATABASE platform_tenant_002;

-- 按时间分表
CREATE TABLE kpi_fact_2025_q1 PARTITION OF kpi_fact
FOR VALUES FROM ('2025-01-01') TO ('2025-04-01');

CREATE TABLE kpi_fact_2025_q2 PARTITION OF kpi_fact
FOR VALUES FROM ('2025-04-01') TO ('2025-07-01');
```

### 14.5 监控与运维

#### 14.5.1 监控体系

**监控指标：**
```yaml
# Prometheus监控配置
monitoring:
  metrics:
    # 业务指标
    business:
      - kpi_calculation_duration
      - settlement_processing_time
      - data_sync_success_rate
      - user_active_sessions

    # 技术指标
    technical:
      - jvm_memory_usage
      - database_connection_pool
      - api_response_time
      - error_rate

    # 基础设施指标
    infrastructure:
      - cpu_usage
      - memory_usage
      - disk_usage
      - network_io
```

**告警规则：**
```yaml
# Grafana告警配置
alerts:
  - name: "API响应时间过长"
    condition: "avg(api_response_time) > 2000ms"
    for: "5m"
    severity: "warning"

  - name: "数据同步失败"
    condition: "data_sync_success_rate < 0.95"
    for: "1m"
    severity: "critical"

  - name: "KPI计算异常"
    condition: "increase(kpi_calculation_errors[5m]) > 10"
    for: "2m"
    severity: "critical"
```

#### 14.5.2 日志管理

**日志分类：**
```json
{
  "business_log": {
    "level": "INFO",
    "format": "JSON",
    "fields": ["timestamp", "user_id", "action", "resource", "result"],
    "retention": "7 years"
  },
  "audit_log": {
    "level": "INFO",
    "format": "JSON",
    "fields": ["timestamp", "user_id", "ip", "action", "before", "after"],
    "retention": "7 years",
    "immutable": true
  },
  "error_log": {
    "level": "ERROR",
    "format": "JSON",
    "fields": ["timestamp", "level", "message", "stack_trace", "context"],
    "retention": "2 years"
  }
}
```

---

## 15. 实施计划

### 15.1 项目里程碑

#### 15.1.1 MVP阶段 (90天)

**第1-2周：项目启动与需求确认**
- 项目团队组建和培训
- 详细需求澄清和确认
- 技术架构设计评审
- 开发环境搭建

**第3-4周：基础架构开发**
- 微服务框架搭建
- 数据库设计和创建
- 基础认证和权限模块
- API网关配置

**第5-8周：核心业务模块开发**
- 项目和合同管理模块
- KPI定义和计算引擎
- 基线管理模块
- 数据集成基础框架

**第9-10周：数据集成和测试**
- ERP系统集成开发
- 数据映射配置
- 单元测试和集成测试
- 性能测试

**第11-12周：用户界面和部署**
- 前端界面开发
- 用户体验测试
- 生产环境部署
- 用户培训和试运行

#### 15.1.2 Phase 2 (6个月)

**月度1-2：扩展功能开发**
- 变更管理完整流程
- 结算引擎优化
- 高级报表功能
- 移动端应用开发

**月度3-4：集成能力增强**
- 银行对账集成
- 电子发票对接
- 更多ERP系统支持
- API生态建设

**月度5-6：智能化功能**
- 数据健康智能监控
- 异常检测算法
- 预测性分析
- 用户体验优化

### 15.2 团队组织架构

#### 15.2.1 项目团队结构

```
项目总监 (1人)
├── 产品经理 (1人)
├── 技术架构师 (1人)
├── 前端团队 (3人)
│   ├── 前端架构师 (1人)
│   └── 前端开发工程师 (2人)
├── 后端团队 (5人)
│   ├── 后端架构师 (1人)
│   ├── 业务开发工程师 (2人)
│   ├── 数据开发工程师 (1人)
│   └── 集成开发工程师 (1人)
├── 测试团队 (2人)
│   ├── 测试经理 (1人)
│   └── 自动化测试工程师 (1人)
├── 运维团队 (2人)
│   ├── DevOps工程师 (1人)
│   └── 数据库管理员 (1人)
└── 实施团队 (2人)
    ├── 实施顾问 (1人)
    └── 培训专员 (1人)
```

#### 15.2.2 角色职责定义

**项目总监：**
- 项目整体规划和进度控制
- 资源协调和风险管理
- 客户沟通和需求管理
- 团队管理和绩效考核

**技术架构师：**
- 技术架构设计和评审
- 技术难点攻关和解决
- 代码质量把控
- 技术团队指导

**产品经理：**
- 产品需求分析和设计
- 用户体验设计
- 产品功能规划
- 市场反馈收集

### 15.3 质量保证计划

#### 15.3.1 测试策略

**测试金字塔：**
```
           E2E测试 (10%)
         ┌─────────────────┐
        │  端到端业务流程   │
       └─────────────────┘

      集成测试 (20%)
    ┌─────────────────────┐
   │   服务间接口测试      │
  └─────────────────────┘

  单元测试 (70%)
┌─────────────────────────┐
│     函数和类测试         │
└─────────────────────────┘
```

**测试覆盖率要求：**
- 单元测试覆盖率：≥80%
- 集成测试覆盖率：≥60%
- 核心业务流程覆盖率：100%

#### 15.3.2 代码质量标准

**代码规范：**
```yaml
# SonarQube质量门禁
quality_gates:
  coverage: ">= 80%"
  duplicated_lines: "< 3%"
  maintainability_rating: "A"
  reliability_rating: "A"
  security_rating: "A"

# 代码审查要求
code_review:
  required_reviewers: 2
  approval_required: true
  automated_checks: true
  security_scan: true
```

### 15.4 风险管理计划

#### 15.4.1 技术风险

| 风险项 | 概率 | 影响 | 风险等级 | 应对措施 |
|--------|------|------|----------|----------|
| ERP集成复杂度超预期 | 中 | 高 | 高 | 提前POC验证，准备备选方案 |
| 性能不达标 | 低 | 高 | 中 | 早期性能测试，架构优化 |
| 数据安全问题 | 低 | 高 | 中 | 安全专家评审，渗透测试 |
| 第三方依赖风险 | 中 | 中 | 中 | 多供应商策略，自研备选 |

#### 15.4.2 业务风险

| 风险项 | 概率 | 影响 | 风险等级 | 应对措施 |
|--------|------|------|----------|----------|
| 需求变更频繁 | 高 | 中 | 高 | 敏捷开发，版本控制 |
| 用户接受度低 | 中 | 高 | 高 | 用户参与设计，培训支持 |
| 竞争对手抢先 | 中 | 中 | 中 | 加快开发进度，差异化 |
| 合规要求变化 | 低 | 高 | 中 | 关注政策动态，灵活架构 |

### 15.5 成本预算

#### 15.5.1 开发成本

| 成本项 | MVP阶段 | Phase 2 | Phase 3 | 总计 |
|--------|---------|---------|---------|------|
| 人力成本 | 180万 | 300万 | 200万 | 680万 |
| 基础设施 | 20万 | 30万 | 20万 | 70万 |
| 第三方服务 | 15万 | 25万 | 15万 | 55万 |
| 测试和部署 | 10万 | 15万 | 10万 | 35万 |
| 培训和支持 | 5万 | 10万 | 5万 | 20万 |
| **总计** | **230万** | **380万** | **250万** | **860万** |

#### 15.5.2 运营成本 (年度)

| 成本项 | SaaS版本 | 私有化版本 |
|--------|----------|------------|
| 云服务费用 | 50万/年 | 0 |
| 运维人员 | 60万/年 | 客户承担 |
| 技术支持 | 40万/年 | 按合同 |
| 系统维护 | 30万/年 | 按合同 |
| **总计** | **180万/年** | **按合同** |

---

## 16. 验收标准

### 16.1 功能验收标准

#### 16.1.1 核心业务流程验收

**基线管理流程：**
- [ ] 能够选择3-12个月的历史数据计算基线
- [ ] 基线计算结果与Excel手工计算误差≤0.1%
- [ ] 基线冻结流程完整，支持电子签名
- [ ] 重基线触发条件准确识别（原材料价格波动>8%）
- [ ] 基线版本管理和对比功能正常

**KPI计算验收：**
- [ ] 支持至少10种预置KPI模板
- [ ] 自定义KPI公式语法检查准确率100%
- [ ] 100万条数据KPI计算在4小时内完成
- [ ] 计算结果可追溯到源数据
- [ ] 异常数据自动剔除机制有效

**变更管理验收：**
- [ ] 变更单从创建到关闭全流程贯通
- [ ] 变更影响评估准确率≥95%
- [ ] 客户电子签名功能正常
- [ ] 变更影响自动回写到项目数据
- [ ] 变更统计和分析报表准确

**结算计算验收：**
- [ ] 成果费计算公式配置灵活
- [ ] 封顶、保底、负保护机制有效
- [ ] 对账单自动生成格式正确
- [ ] 结算明细可追溯到KPI计算过程
- [ ] 多币种结算处理正确

#### 16.1.2 数据集成验收

**ERP系统集成：**
- [ ] 成功对接金蝶K3 Cloud和用友U8+
- [ ] 数据同步成功率≥99%
- [ ] 数据映射配置界面友好
- [ ] 同步失败自动重试机制有效
- [ ] 数据健康评分算法准确

**文件导入验证：**
- [ ] Excel/CSV模板导入成功率≥95%
- [ ] 数据格式验证准确
- [ ] 批量导入性能满足要求
- [ ] 导入错误提示清晰
- [ ] 导入历史记录完整

### 16.2 性能验收标准

#### 16.2.1 响应时间要求

| 功能 | 响应时间要求 | 验收标准 |
|------|-------------|----------|
| 用户登录 | ≤2秒 | 95%的请求在2秒内完成 |
| 项目列表查询 | ≤3秒 | 包含100个项目的列表 |
| KPI仪表盘加载 | ≤5秒 | 包含10个KPI图表 |
| 报表导出 | ≤30秒 | 10万条数据导出 |
| 大批量计算 | ≤4小时 | 100万条数据KPI计算 |

#### 16.2.2 并发性能要求

| 场景 | 并发用户数 | 响应时间 | 成功率 |
|------|------------|----------|--------|
| 正常业务操作 | 100用户 | ≤3秒 | ≥99% |
| 报表查询 | 50用户 | ≤5秒 | ≥95% |
| 数据导入 | 10用户 | ≤10秒 | ≥90% |
| KPI计算 | 5用户 | ≤30秒 | ≥95% |

### 16.3 安全验收标准

#### 16.3.1 身份认证和授权

- [ ] 支持多因子认证(MFA)
- [ ] 密码策略符合安全要求
- [ ] 会话超时自动登出
- [ ] 权限控制粒度到字段级
- [ ] 审计日志记录完整

#### 16.3.2 数据安全

- [ ] 敏感数据传输加密(TLS 1.3)
- [ ] 敏感字段存储加密
- [ ] 数据备份加密存储
- [ ] 数据脱敏功能有效
- [ ] 数据访问日志完整

### 16.4 可用性验收标准

#### 16.4.1 系统可用性

- [ ] SaaS版本月度可用性≥99.9%
- [ ] 私有化版本月度可用性≥99.5%
- [ ] 故障恢复时间≤1小时
- [ ] 数据备份恢复成功率100%
- [ ] 灾难恢复演练通过

#### 16.4.2 用户体验

- [ ] 界面响应式设计适配多设备
- [ ] 关键操作有明确反馈
- [ ] 错误提示信息清晰
- [ ] 帮助文档完整
- [ ] 用户培训材料齐全

### 16.5 集成验收标准

#### 16.5.1 系统集成

- [ ] 与主流ERP系统集成成功
- [ ] API接口文档完整准确
- [ ] Webhook事件推送正常
- [ ] 第三方系统调用成功率≥99%
- [ ] 集成监控和告警有效

#### 16.5.2 数据一致性

- [ ] 跨系统数据一致性≥99%
- [ ] 数据同步延迟≤5分钟
- [ ] 数据冲突处理机制有效
- [ ] 数据质量监控准确
- [ ] 异常数据处理及时

---

## 17. 风险评估

### 17.1 技术风险分析

#### 17.1.1 高风险项

**1. ERP系统集成复杂性**
- **风险描述**：不同ERP系统数据结构差异大，集成难度超预期
- **影响程度**：可能导致项目延期1-2个月
- **发生概率**：中等(40%)
- **应对措施**：
  - 提前进行技术调研和POC验证
  - 准备标准化数据接口和适配器模式
  - 与ERP厂商建立技术合作关系
  - 制定备选的文件导入方案

**2. 大数据量性能挑战**
- **风险描述**：百万级数据计算性能不达标
- **影响程度**：影响用户体验，可能需要架构重构
- **发生概率**：中等(30%)
- **应对措施**：
  - 早期进行性能测试和优化
  - 采用分布式计算架构
  - 实施数据分片和缓存策略
  - 准备云原生弹性扩展方案

#### 17.1.2 中风险项

**3. 数据安全合规要求**
- **风险描述**：数据保护法规要求可能超出当前设计
- **影响程度**：需要增加安全功能开发
- **发生概率**：低等(20%)
- **应对措施**：
  - 参考等保二级标准设计
  - 引入安全专家进行评审
  - 实施数据分类分级保护
  - 建立合规检查机制

### 17.2 业务风险分析

#### 17.2.1 市场风险

**1. 竞争对手抢先发布**
- **风险描述**：竞争对手可能推出类似产品
- **影响程度**：市场先发优势丧失
- **发生概率**：中等(35%)
- **应对措施**：
  - 加快MVP开发进度
  - 强化产品差异化特性
  - 建立客户粘性和壁垒
  - 申请相关技术专利

**2. 客户接受度不足**
- **风险描述**：目标客户对新模式接受度低
- **影响程度**：影响产品推广和收入
- **发生概率**：中等(30%)
- **应对措施**：
  - 深度参与客户需求调研
  - 提供充分的ROI证明
  - 建立标杆客户案例
  - 提供完善的培训支持

#### 17.2.2 运营风险

**3. 团队人员流失**
- **风险描述**：核心技术人员离职
- **影响程度**：项目进度延迟，知识流失
- **发生概率**：中等(25%)
- **应对措施**：
  - 建立有竞争力的薪酬体系
  - 提供良好的职业发展通道
  - 实施知识管理和文档化
  - 建立人员备份机制

### 17.3 合规风险分析

#### 17.3.1 法律法规风险

**1. 数据跨境传输限制**
- **风险描述**：数据本地化要求影响SaaS部署
- **影响程度**：需要调整部署架构
- **发生概率**：中等(30%)
- **应对措施**：
  - 采用多区域部署架构
  - 实施数据本地化存储
  - 建立合规审查机制
  - 准备私有化部署方案

**2. 财务数据处理合规**
- **风险描述**：财务数据处理不符合会计准则
- **影响程度**：影响产品合规性和客户信任
- **发生概率**：低等(15%)
- **应对措施**：
  - 咨询财务和法务专家
  - 参考国际会计准则设计
  - 建立合规检查流程
  - 提供审计支持功能

### 17.4 风险监控和应对

#### 17.4.1 风险监控机制

**技术风险监控：**
```yaml
# 技术风险监控指标
technical_risks:
  performance:
    - metric: "api_response_time_p95"
      threshold: 2000ms
      alert_level: "warning"
    - metric: "kpi_calculation_duration"
      threshold: 4hours
      alert_level: "critical"

  integration:
    - metric: "erp_sync_success_rate"
      threshold: 0.99
      alert_level: "warning"
    - metric: "data_quality_score"
      threshold: 80
      alert_level: "critical"
```

**业务风险监控：**
```yaml
# 业务风险监控指标
business_risks:
  adoption:
    - metric: "user_active_rate"
      threshold: 0.8
      alert_level: "warning"
    - metric: "feature_usage_rate"
      threshold: 0.6
      alert_level: "warning"

  satisfaction:
    - metric: "customer_satisfaction_score"
      threshold: 4.0
      alert_level: "warning"
    - metric: "support_ticket_resolution_time"
      threshold: 24hours
      alert_level: "warning"
```

#### 17.4.2 应急响应计划

**技术故障应急：**
1. **故障检测**：自动监控系统检测异常
2. **故障分级**：按影响范围和严重程度分级
3. **应急响应**：启动相应级别的应急预案
4. **故障恢复**：执行恢复操作并验证
5. **事后分析**：分析故障原因并改进

**业务风险应急：**
1. **风险预警**：建立早期预警机制
2. **风险评估**：评估风险影响和概率
3. **应对措施**：执行预定的应对策略
4. **效果监控**：监控应对措施效果
5. **持续改进**：优化风险管理流程

---

## 18. 附录

### 18.1 样例对账单

#### 18.1.1 季度成果费对账单

```
┌─────────────────────────────────────────────────────────────┐
│                    项目制按结果买单平台                      │
│                   2025年第一季度成果费对账单                 │
└─────────────────────────────────────────────────────────────┘

基本信息
─────────────────────────────────────────────────────────────
客户名称：    ABC智能制造有限公司
合同编号：    CONT-2024-001
项目名称：    智能制造系统集成项目
结算周期：    2025年第一季度 (2025-01-01 至 2025-03-31)
结算日期：    2025-04-05
公式版本：    v1.2
数据健康评分： 92分 ✓

KPI改进情况
─────────────────────────────────────────────────────────────
┌──────────┬─────────┬─────────┬─────────┬─────────────┐
│ 指标名称  │ 基线值   │ 当前值   │ 改进幅度 │ 改进金额(元) │
├──────────┼─────────┼─────────┼─────────┼─────────────┤
│ 毛利率    │ 15.0%   │ 17.2%   │ +2.2%   │ +110,000    │
│ OTD率     │ 85.0%   │ 92.3%   │ +7.3%   │ +36,500     │
│ DSO天数   │ 60天    │ 45天    │ -15天   │ +25,000     │
│ 返工率    │ 3.2%    │ 2.1%    │ -1.1%   │ +15,500     │
│ PPV节省   │ 基准    │ 实际    │ -2.5%   │ +8,000      │
└──────────┴─────────┴─────────┴─────────┴─────────────┘

成果费计算明细
─────────────────────────────────────────────────────────────
┌────────────────────┬───────────┬──────┬───────────┐
│ 计算项目            │ 改进金额   │ 比例  │ 成果费    │
├────────────────────┼───────────┼──────┼───────────┤
│ 毛利改进            │ 110,000   │ 15%  │ 16,500    │
│ 交期改进(罚款节省)   │ 36,500    │ 20%  │ 7,300     │
│ 现金流改进          │ 25,000    │ 30%  │ 7,500     │
│ 质量改进(返工节省)   │ 15,500    │ 10%  │ 1,550     │
│ 采购优化(PPV节省)   │ 8,000     │ 25%  │ 2,000     │
├────────────────────┼───────────┼──────┼───────────┤
│ 小计               │           │      │ 34,850    │
│ 封顶调整(2.5倍订阅费)│          │      │ -9,850    │
├────────────────────┼───────────┼──────┼───────────┤
│ 最终成果费          │           │      │ 25,000    │
└────────────────────┴───────────┴──────┴───────────┘

计算公式说明
─────────────────────────────────────────────────────────────
成果费 = 0.15×毛利改进 + 0.20×交期改进 + 0.30×现金改进
        + 0.10×质量改进 + 0.25×采购优化
封顶限制：不超过季度订阅费的2.5倍 (35,000元)
负向保护：成果费最低为0元

数据来源说明
─────────────────────────────────────────────────────────────
• 财务数据来源：金蝶K3 Cloud系统
• 项目数据来源：项目管理系统
• 采购数据来源：采购管理系统
• 数据同步时间：每日凌晨2:00
• 数据健康评分：92分 (完整性95%，时效性90%，一致性88%)

异常剔除说明
─────────────────────────────────────────────────────────────
• 钢材价格上涨12%，超过8%阈值，相关成本影响已剔除
• 客户要求延期2周，相关OTD影响已剔除
• 春节假期影响已按工作日调整

审计轨迹
─────────────────────────────────────────────────────────────
• 基线冻结：2024-12-15，冻结人：张财务总监
• 数据计算：2025-04-01，计算版本：v1.2.3
• 结果审核：2025-04-03，审核人：李项目经理
• 客户确认：待确认

确认签署
─────────────────────────────────────────────────────────────
服务方确认：
签名：________________    日期：2025-04-05
姓名：张三 (项目经理)

客户方确认：
签名：________________    日期：__________
姓名：________________ (客户代表)

备注：本对账单一式两份，双方各执一份。
      如有异议，请在收到对账单后5个工作日内提出。

┌─────────────────────────────────────────────────────────────┐
│ 审计包下载：https://platform.com/audit/2025Q1_ABC_001.zip   │
│ 在线查看：  https://platform.com/settlement/settle_001      │
└─────────────────────────────────────────────────────────────┘
```

### 18.2 KPI配置示例

#### 18.2.1 毛利率KPI配置

```json
{
  "kpi_definition": {
    "kpi_id": "kpi_margin_rate",
    "kpi_name": "项目毛利率",
    "kpi_code": "MARGIN_RATE",
    "category": "FINANCIAL",
    "description": "项目毛利占项目收入的比例，反映项目盈利能力",
    "unit": "%",
    "target_value": 20.0,
    "formula": {
      "expression": "(sum(Revenue) - sum(Cost)) / sum(Revenue) * 100",
      "variables": {
        "Revenue": {
          "source_table": "project_revenue",
          "filter": "revenue_type IN ('CONTRACT', 'CHANGE')",
          "aggregation": "SUM",
          "field": "revenue_amount"
        },
        "Cost": {
          "source_table": "project_cost",
          "filter": "is_controllable = true",
          "aggregation": "SUM",
          "field": "cost_amount"
        }
      }
    },
    "dimensions": ["project_id", "customer_id", "period"],
    "calculation_frequency": "DAILY",
    "exclude_rules": [
      {
        "condition": "customer_caused_delay = true",
        "description": "客户原因导致的延期影响"
      },
      {
        "condition": "force_majeure = true",
        "description": "不可抗力因素影响"
      }
    ],
    "data_sources": [
      "erp_financial_data",
      "project_management_system"
    ],
    "version": "v1.2",
    "created_by": "admin",
    "created_at": "2024-12-01T10:00:00Z"
  }
}
```

#### 18.2.2 OTD准时交付率配置

```json
{
  "kpi_definition": {
    "kpi_id": "kpi_otd_rate",
    "kpi_name": "准时交付率",
    "kpi_code": "OTD_RATE",
    "category": "OPERATIONAL",
    "description": "按时完成里程碑的比例，反映项目执行效率",
    "unit": "%",
    "target_value": 95.0,
    "formula": {
      "expression": "sum(OnTimeMilestones) / sum(TotalMilestones) * 100",
      "variables": {
        "OnTimeMilestones": {
          "source_table": "milestone",
          "filter": "actual_date <= planned_date AND status = 'COMPLETED'",
          "aggregation": "COUNT",
          "field": "milestone_id",
          "weight_field": "weight"
        },
        "TotalMilestones": {
          "source_table": "milestone",
          "filter": "status IN ('COMPLETED', 'DELAYED')",
          "aggregation": "COUNT",
          "field": "milestone_id",
          "weight_field": "weight"
        }
      }
    },
    "dimensions": ["project_id", "milestone_type", "period"],
    "calculation_frequency": "DAILY",
    "exclude_rules": [
      {
        "condition": "delay_reason = 'CUSTOMER_REQUEST'",
        "description": "客户要求延期的里程碑"
      },
      {
        "condition": "delay_reason = 'FORCE_MAJEURE'",
        "description": "不可抗力导致的延期"
      }
    ],
    "milestone_types": [
      {"type": "DESIGN", "weight": 0.2},
      {"type": "PROCUREMENT", "weight": 0.3},
      {"type": "PRODUCTION", "weight": 0.3},
      {"type": "TESTING", "weight": 0.1},
      {"type": "DELIVERY", "weight": 0.1}
    ],
    "version": "v1.1",
    "created_by": "pm_manager",
    "created_at": "2024-12-01T10:30:00Z"
  }
}
```

### 18.3 API接口文档示例

#### 18.3.1 项目查询接口

```yaml
openapi: 3.0.0
info:
  title: 项目制按结果买单平台API
  version: 1.0.0
  description: 项目管理相关API接口

paths:
  /api/v1/projects:
    get:
      summary: 查询项目列表
      description: 根据条件查询项目列表，支持分页和排序
      parameters:
        - name: status
          in: query
          description: 项目状态
          schema:
            type: string
            enum: [PLANNING, ONGOING, COMPLETED, CANCELLED]
        - name: customer_id
          in: query
          description: 客户ID
          schema:
            type: string
        - name: manager_id
          in: query
          description: 项目经理ID
          schema:
            type: string
        - name: page
          in: query
          description: 页码，从1开始
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: size
          in: query
          description: 每页大小
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: sort
          in: query
          description: 排序字段
          schema:
            type: string
            default: "created_at"
        - name: order
          in: query
          description: 排序方向
          schema:
            type: string
            enum: [asc, desc]
            default: "desc"
      responses:
        '200':
          description: 查询成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 200
                  message:
                    type: string
                    example: "success"
                  data:
                    type: object
                    properties:
                      total:
                        type: integer
                        description: 总记录数
                        example: 150
                      page:
                        type: integer
                        description: 当前页码
                        example: 1
                      size:
                        type: integer
                        description: 每页大小
                        example: 20
                      items:
                        type: array
                        items:
                          $ref: '#/components/schemas/Project'
        '400':
          description: 请求参数错误
        '401':
          description: 未授权
        '403':
          description: 权限不足

components:
  schemas:
    Project:
      type: object
      properties:
        project_id:
          type: string
          description: 项目ID
          example: "proj_001"
        project_no:
          type: string
          description: 项目编号
          example: "PROJ-2025-001"
        project_name:
          type: string
          description: 项目名称
          example: "智能制造系统集成项目"
        customer_name:
          type: string
          description: 客户名称
          example: "ABC智能制造有限公司"
        project_manager:
          type: string
          description: 项目经理
          example: "张三"
        project_status:
          type: string
          description: 项目状态
          enum: [PLANNING, ONGOING, COMPLETED, CANCELLED]
          example: "ONGOING"
        budget_amount:
          type: number
          description: 预算金额
          example: 5000000.00
        actual_cost:
          type: number
          description: 实际成本
          example: 3200000.00
        margin_rate:
          type: number
          description: 毛利率(%)
          example: 17.2
        otd_rate:
          type: number
          description: 准时交付率(%)
          example: 92.3
        progress:
          type: number
          description: 项目进度(%)
          example: 65.5
        planned_start_date:
          type: string
          format: date
          description: 计划开始日期
          example: "2024-10-01"
        planned_end_date:
          type: string
          format: date
          description: 计划结束日期
          example: "2025-06-30"
        created_at:
          type: string
          format: date-time
          description: 创建时间
          example: "2024-09-15T10:00:00Z"
```

### 18.4 部署配置示例

#### 18.4.1 Kubernetes部署配置

```yaml
# namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: platform-prod
  labels:
    name: platform-prod

---
# configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: platform-config
  namespace: platform-prod
data:
  application.yml: |
    server:
      port: 8080
    spring:
      datasource:
        master:
          url: ***********************************************
          username: ${DB_USERNAME}
          password: ${DB_PASSWORD}
        slave:
          url: **********************************************
          username: ${DB_READONLY_USERNAME}
          password: ${DB_READONLY_PASSWORD}
      redis:
        host: redis-cluster
        port: 6379
        password: ${REDIS_PASSWORD}
      kafka:
        bootstrap-servers: kafka-cluster:9092

    platform:
      security:
        jwt:
          secret: ${JWT_SECRET}
          expiration: 3600
      integration:
        kingdee:
          api-url: ${KINGDEE_API_URL}
          app-id: ${KINGDEE_APP_ID}
          app-secret: ${KINGDEE_APP_SECRET}

---
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: platform-api
  namespace: platform-prod
spec:
  replicas: 3
  selector:
    matchLabels:
      app: platform-api
  template:
    metadata:
      labels:
        app: platform-api
    spec:
      containers:
      - name: platform-api
        image: platform/api:v1.0.0
        ports:
        - containerPort: 8080
        env:
        - name: DB_USERNAME
          valueFrom:
            secretKeyRef:
              name: platform-secrets
              key: db-username
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: platform-secrets
              key: db-password
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: platform-secrets
              key: redis-password
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: platform-secrets
              key: jwt-secret
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /actuator/health/readiness
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
      volumes:
      - name: config-volume
        configMap:
          name: platform-config

---
# service.yaml
apiVersion: v1
kind: Service
metadata:
  name: platform-api-service
  namespace: platform-prod
spec:
  selector:
    app: platform-api
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8080
  type: ClusterIP

---
# ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: platform-ingress
  namespace: platform-prod
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
spec:
  tls:
  - hosts:
    - api.platform.com
    secretName: platform-tls
  rules:
  - host: api.platform.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: platform-api-service
            port:
              number: 80
```

---

## 总结

本软件需求规格说明书(SRS)详细描述了项目制按结果买单平台的完整需求，包括：

### 核心特性
1. **统一数据模型**：连接合同-项目-采购-工时-质量-财务全链条
2. **智能KPI引擎**：支持复杂业务规则和自定义计算公式
3. **基线管理**：建立可审计的改进基准和版本控制
4. **结算引擎**：灵活的绩效计费模型和自动化结算
5. **数据健康监控**：确保数据质量和计算可靠性

### 技术亮点
1. **微服务架构**：支持高并发和弹性扩展
2. **多租户设计**：企业级数据隔离和权限控制
3. **实时计算**：流处理和批处理相结合
4. **智能集成**：支持主流ERP系统无缝对接
5. **安全合规**：等保二级标准和数据加密保护

### 商业价值
1. **提效降本**：项目毛利率提升10-30%，回款周期缩短20-40%
2. **风险控制**：变更管理闭环，数据异常自动检测
3. **合作共赢**：按结果付费模式，激励持续改进
4. **审计合规**：完整的操作轨迹和审计支持

该平台将为项目型制造企业提供数字化转型的核心工具，通过"可审计的结果KPI+结算引擎"实现真正的按结果买单，推动行业向更高效、更透明、更可持续的方向发展。
```