import {
  __commonJS
} from "./chunk-DC5AMYBS.js";

// node_modules/dayjs/plugin/updateLocale.js
var require_updateLocale = __commonJS({
  "node_modules/dayjs/plugin/updateLocale.js"(exports, module) {
    !function(e, n) {
      "object" == typeof exports && "undefined" != typeof module ? module.exports = n() : "function" == typeof define && define.amd ? define(n) : (e = "undefined" != typeof globalThis ? globalThis : e || self).dayjs_plugin_updateLocale = n();
    }(exports, function() {
      "use strict";
      return function(e, n, t) {
        t.updateLocale = function(e2, n2) {
          var o = t.Ls[e2];
          if (o) return (n2 ? Object.keys(n2) : []).forEach(function(e3) {
            o[e3] = n2[e3];
          }), o;
        };
      };
    });
  }
});
export default require_updateLocale();
//# sourceMappingURL=dayjs_plugin_updateLocale.js.map
