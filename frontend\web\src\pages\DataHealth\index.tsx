import React, { useEffect, useState } from 'react';
import {
  Card,
  Typography,
  Row,
  Col,
  Progress,
  Table,
  Tag,
  Button,
  Select,
  Space,
  Tooltip,
  Alert,
  Statistic,
  List,
  Badge,
} from 'antd';
import {
  ReloadOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  WarningOutlined,
  CloseCircleOutlined,
  SyncOutlined,
  BarChartOutlined,
} from '@ant-design/icons';
import { useAppDispatch } from '@store/index';
import { setPageInfo } from '@store/slices/uiSlice';
import { mockData } from '@mock/data';

const { Title, Text } = Typography;
const { Option } = Select;

interface DataHealthMetric {
  name: string;
  score: number;
  status: 'excellent' | 'good' | 'warning' | 'poor';
  issues: number;
  lastUpdated: string;
}

const DataHealth: React.FC = () => {
  const dispatch = useAppDispatch();
  const [selectedProject, setSelectedProject] = useState<string>('all');
  const [refreshing, setRefreshing] = useState(false);

  // 使用模拟数据
  const dataHealthList = mockData.dataHealth;

  useEffect(() => {
    dispatch(setPageInfo({
      title: '数据健康',
      description: '监控数据质量和健康状态'
    }));
  }, [dispatch]);

  const handleRefresh = async () => {
    setRefreshing(true);
    // 模拟刷新延迟
    setTimeout(() => {
      setRefreshing(false);
    }, 2000);
  };

  const getHealthStatus = (score: number) => {
    if (score >= 95) return 'excellent';
    if (score >= 85) return 'good';
    if (score >= 70) return 'warning';
    return 'poor';
  };

  const getHealthColor = (status: string) => {
    const colors = {
      excellent: '#52c41a',
      good: '#1890ff',
      warning: '#faad14',
      poor: '#ff4d4f',
    };
    return colors[status as keyof typeof colors] || '#d9d9d9';
  };

  const getHealthIcon = (status: string) => {
    const icons = {
      excellent: <CheckCircleOutlined style={{ color: '#52c41a' }} />,
      good: <CheckCircleOutlined style={{ color: '#1890ff' }} />,
      warning: <WarningOutlined style={{ color: '#faad14' }} />,
      poor: <CloseCircleOutlined style={{ color: '#ff4d4f' }} />,
    };
    return icons[status as keyof typeof icons] || null;
  };

  const getSeverityColor = (severity: string) => {
    const colors = {
      CRITICAL: 'red',
      HIGH: 'orange',
      MEDIUM: 'yellow',
      LOW: 'blue',
    };
    return colors[severity as keyof typeof colors] || 'default';
  };

  // 计算总体健康指标
  const overallMetrics: DataHealthMetric[] = [
    {
      name: '数据完整性',
      score: 92,
      status: 'good',
      issues: 3,
      lastUpdated: '2025-01-14 10:30:00',
    },
    {
      name: '数据时效性',
      score: 88,
      status: 'good',
      issues: 5,
      lastUpdated: '2025-01-14 10:25:00',
    },
    {
      name: '数据一致性',
      score: 76,
      status: 'warning',
      issues: 8,
      lastUpdated: '2025-01-14 10:20:00',
    },
    {
      name: '数据准确性',
      score: 94,
      status: 'excellent',
      issues: 1,
      lastUpdated: '2025-01-14 10:35:00',
    },
  ];

  const overallScore = Math.round(
    overallMetrics.reduce((sum, metric) => sum + metric.score, 0) / overallMetrics.length
  );

  const columns = [
    {
      title: '项目名称',
      dataIndex: 'projectName',
      key: 'projectName',
      width: 200,
    },
    {
      title: '总体评分',
      dataIndex: 'overallScore',
      key: 'overallScore',
      width: 120,
      render: (score: number) => (
        <div className="flex items-center">
          {getHealthIcon(getHealthStatus(score))}
          <Text className="ml-2" strong>{score}分</Text>
        </div>
      ),
    },
    {
      title: '完整性',
      dataIndex: 'completenessScore',
      key: 'completenessScore',
      width: 100,
      render: (score: number) => (
        <Progress
          percent={score}
          size="small"
          strokeColor={getHealthColor(getHealthStatus(score))}
          format={() => `${score}%`}
        />
      ),
    },
    {
      title: '时效性',
      dataIndex: 'timelinessScore',
      key: 'timelinessScore',
      width: 100,
      render: (score: number) => (
        <Progress
          percent={score}
          size="small"
          strokeColor={getHealthColor(getHealthStatus(score))}
          format={() => `${score}%`}
        />
      ),
    },
    {
      title: '一致性',
      dataIndex: 'consistencyScore',
      key: 'consistencyScore',
      width: 100,
      render: (score: number) => (
        <Progress
          percent={score}
          size="small"
          strokeColor={getHealthColor(getHealthStatus(score))}
          format={() => `${score}%`}
        />
      ),
    },
    {
      title: '准确性',
      dataIndex: 'accuracyScore',
      key: 'accuracyScore',
      width: 100,
      render: (score: number) => (
        <Progress
          percent={score}
          size="small"
          strokeColor={getHealthColor(getHealthStatus(score))}
          format={() => `${score}%`}
        />
      ),
    },
    {
      title: '问题数量',
      dataIndex: 'issues',
      key: 'issues',
      width: 100,
      render: (issues: any[]) => (
        <Badge count={issues.length} showZero>
          <Button type="text" icon={<ExclamationCircleOutlined />} />
        </Badge>
      ),
    },
    {
      title: '最后更新',
      dataIndex: 'lastUpdated',
      key: 'lastUpdated',
      width: 150,
      render: (date: string) => date.substring(0, 16).replace('T', ' '),
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_, record: any) => (
        <Space>
          <Tooltip title="查看详情">
            <Button type="text" icon={<BarChartOutlined />} />
          </Tooltip>
          <Tooltip title="刷新数据">
            <Button type="text" icon={<SyncOutlined />} />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      {/* 总体健康状态 */}
      <Row gutter={[16, 16]}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总体健康评分"
              value={overallScore}
              suffix="/100"
              valueStyle={{
                color: getHealthColor(getHealthStatus(overallScore)),
                fontSize: '32px',
                fontWeight: 600,
              }}
              prefix={getHealthIcon(getHealthStatus(overallScore))}
            />
          </Card>
        </Col>
        {overallMetrics.map((metric, index) => (
          <Col span={6} key={index}>
            <Card>
              <Statistic
                title={metric.name}
                value={metric.score}
                suffix="/100"
                valueStyle={{
                  color: getHealthColor(metric.status),
                  fontSize: '24px',
                  fontWeight: 600,
                }}
                prefix={getHealthIcon(metric.status)}
              />
              <div className="mt-2">
                <Text type="secondary" className="text-xs">
                  {metric.issues}个问题 · {metric.lastUpdated.substring(11, 16)}更新
                </Text>
              </div>
            </Card>
          </Col>
        ))}
      </Row>

      {/* 健康状态警告 */}
      {overallScore < 80 && (
        <Alert
          message="数据健康状态需要关注"
          description="检测到多个项目存在数据质量问题，建议及时处理以确保结算准确性。"
          type="warning"
          showIcon
          action={
            <Button size="small" type="primary">
              查看详情
            </Button>
          }
        />
      )}

      {/* 项目健康状态列表 */}
      <Card>
        <div className="flex items-center justify-between mb-4">
          <Title level={4} className="mb-0">项目数据健康状态</Title>
          <Space>
            <Select
              placeholder="选择项目"
              style={{ width: 200 }}
              value={selectedProject}
              onChange={setSelectedProject}
            >
              <Option value="all">全部项目</Option>
              <Option value="proj_001">智能制造系统集成项目</Option>
              <Option value="proj_002">数字化转型咨询项目</Option>
              <Option value="proj_003">云平台建设项目</Option>
            </Select>
            <Button
              icon={<ReloadOutlined />}
              loading={refreshing}
              onClick={handleRefresh}
            >
              刷新
            </Button>
          </Space>
        </div>

        <Table
          columns={columns}
          dataSource={dataHealthList}
          rowKey="projectId"
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
        />
      </Card>

      {/* 常见问题列表 */}
      <Row gutter={16}>
        <Col span={12}>
          <Card title="高优先级问题" extra={<Badge count={5} />}>
            <List
              size="small"
              dataSource={[
                {
                  title: '项目成本数据缺失',
                  description: '智能制造项目11月成本数据未录入',
                  severity: 'CRITICAL',
                  time: '2小时前',
                },
                {
                  title: '客户满意度数据延迟',
                  description: '数字化转型项目客户评分数据延迟3天',
                  severity: 'HIGH',
                  time: '4小时前',
                },
                {
                  title: '里程碑状态不一致',
                  description: '云平台项目里程碑状态与实际进度不符',
                  severity: 'MEDIUM',
                  time: '6小时前',
                },
              ]}
              renderItem={(item) => (
                <List.Item
                  actions={[
                    <Button type="link" size="small">处理</Button>
                  ]}
                >
                  <List.Item.Meta
                    title={
                      <div className="flex items-center">
                        <Tag color={getSeverityColor(item.severity)} className="mr-2">
                          {item.severity}
                        </Tag>
                        {item.title}
                      </div>
                    }
                    description={
                      <div>
                        <div>{item.description}</div>
                        <Text type="secondary" className="text-xs">{item.time}</Text>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
        <Col span={12}>
          <Card title="数据质量趋势" extra={<Button type="link">查看更多</Button>}>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between mb-2">
                  <Text>本周数据完整性</Text>
                  <Text strong>92%</Text>
                </div>
                <Progress percent={92} strokeColor="#52c41a" />
              </div>
              <div>
                <div className="flex justify-between mb-2">
                  <Text>本周数据时效性</Text>
                  <Text strong>88%</Text>
                </div>
                <Progress percent={88} strokeColor="#1890ff" />
              </div>
              <div>
                <div className="flex justify-between mb-2">
                  <Text>本周数据准确性</Text>
                  <Text strong>94%</Text>
                </div>
                <Progress percent={94} strokeColor="#52c41a" />
              </div>
              <div className="mt-4 p-3 bg-gray-50 rounded">
                <Text type="secondary" className="text-sm">
                  相比上周，数据完整性提升了3%，时效性下降了2%，准确性保持稳定。
                </Text>
              </div>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default DataHealth;
