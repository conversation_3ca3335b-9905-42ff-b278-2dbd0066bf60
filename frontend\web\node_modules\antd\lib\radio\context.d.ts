import * as React from 'react';
import type { RadioGroupContextProps } from './interface';
declare const RadioGroupContext: React.Context<RadioGroupContextProps | null>;
export declare const RadioGroupContextProvider: React.Provider<RadioGroupContextProps | null>;
export default RadioGroupContext;
export declare const RadioOptionTypeContext: React.Context<import("./interface").RadioGroupOptionType | null>;
export declare const RadioOptionTypeContextProvider: React.Provider<import("./interface").RadioGroupOptionType | null>;
