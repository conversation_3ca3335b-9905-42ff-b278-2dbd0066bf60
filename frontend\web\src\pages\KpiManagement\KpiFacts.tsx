import React, { useEffect, useState } from 'react';
import {
  Table,
  Card,
  Button,
  Input,
  Select,
  Space,
  Tag,
  Typography,
  DatePicker,
  Statistic,
  Row,
  Col,
  Progress,
  Tooltip,
  Modal,
  message,
} from 'antd';
import {
  SearchOutlined,
  ReloadOutlined,
  ExportOutlined,
  CalculatorOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  MinusOutlined,
  EyeOutlined,
} from '@ant-design/icons';
import { useAppDispatch, useAppSelector } from '@store/index';
import { fetchKpiFacts, calculateKpi } from '@store/slices/kpiSlice';
import { setPageInfo } from '@store/slices/uiSlice';
import { KpiFact } from '@types/index';
import dayjs from 'dayjs';

const { Text } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;

const KpiFacts: React.FC = () => {
  const dispatch = useAppDispatch();
  const { facts, loading, calculating } = useAppSelector(state => state.kpi);
  const [selectedPeriod, setSelectedPeriod] = useState<string>('2024-12');
  const [selectedProject, setSelectedProject] = useState<string>('all');

  useEffect(() => {
    dispatch(setPageInfo({
      title: 'KPI数据',
      description: 'KPI实际数据和计算结果'
    }));
  }, [dispatch]);

  useEffect(() => {
    dispatch(fetchKpiFacts({
      period: selectedPeriod,
      projectId: selectedProject === 'all' ? undefined : selectedProject,
    }));
  }, [dispatch, selectedPeriod, selectedProject]);

  const handleCalculateKpi = () => {
    Modal.confirm({
      title: '确认计算KPI',
      content: `确定要重新计算${selectedPeriod}期间的KPI数据吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          await dispatch(calculateKpi({
            period: selectedPeriod,
            scope: selectedProject === 'all' ? ['all'] : [selectedProject],
            forceRecalculate: true,
          })).unwrap();
          message.success('KPI计算完成');
          // 重新获取数据
          dispatch(fetchKpiFacts({
            period: selectedPeriod,
            projectId: selectedProject === 'all' ? undefined : selectedProject,
          }));
        } catch (error: any) {
          message.error(error.message || 'KPI计算失败');
        }
      },
    });
  };

  const getTrendIcon = (trend: number) => {
    if (trend > 0) return <ArrowUpOutlined style={{ color: '#52c41a' }} />;
    if (trend < 0) return <ArrowDownOutlined style={{ color: '#ff4d4f' }} />;
    return <MinusOutlined style={{ color: '#8c8c8c' }} />;
  };

  const getValueColor = (value: number, target?: number) => {
    if (!target) return 'default';
    if (value >= target) return 'success';
    if (value >= target * 0.8) return 'warning';
    return 'danger';
  };

  const columns = [
    {
      title: 'KPI名称',
      dataIndex: 'kpiName',
      key: 'kpiName',
      width: 150,
      fixed: 'left' as const,
    },
    {
      title: '项目名称',
      dataIndex: 'projectName',
      key: 'projectName',
      width: 200,
    },
    {
      title: '计量期间',
      dataIndex: 'measurePeriod',
      key: 'measurePeriod',
      width: 100,
    },
    {
      title: '实际值',
      dataIndex: 'actualValue',
      key: 'actualValue',
      width: 100,
      render: (value: number, record: KpiFact) => (
        <Text strong type={getValueColor(value, record.targetValue)}>
          {value}{record.unit}
        </Text>
      ),
    },
    {
      title: '目标值',
      dataIndex: 'targetValue',
      key: 'targetValue',
      width: 100,
      render: (value: number, record: KpiFact) => (
        <Text>{value ? `${value}${record.unit}` : '-'}</Text>
      ),
    },
    {
      title: '基线值',
      dataIndex: 'baselineValue',
      key: 'baselineValue',
      width: 100,
      render: (value: number, record: KpiFact) => (
        <Text>{value ? `${value}${record.unit}` : '-'}</Text>
      ),
    },
    {
      title: '达成率',
      dataIndex: 'achievementRate',
      key: 'achievementRate',
      width: 120,
      render: (rate: number) => (
        <Progress
          percent={Math.min(rate, 150)}
          size="small"
          status={rate >= 100 ? 'success' : rate >= 80 ? 'active' : 'exception'}
          format={() => `${rate}%`}
        />
      ),
    },
    {
      title: '改善幅度',
      dataIndex: 'improvementRate',
      key: 'improvementRate',
      width: 120,
      render: (rate: number) => (
        <div className="flex items-center">
          {getTrendIcon(rate)}
          <Text className="ml-1" type={rate > 0 ? 'success' : rate < 0 ? 'danger' : 'secondary'}>
            {rate > 0 ? '+' : ''}{rate}%
          </Text>
        </div>
      ),
    },
    {
      title: '数据来源',
      dataIndex: 'dataSource',
      key: 'dataSource',
      width: 100,
      render: (source: string) => (
        <Tag color={source === 'AUTO' ? 'blue' : 'orange'}>
          {source === 'AUTO' ? '自动' : '手动'}
        </Tag>
      ),
    },
    {
      title: '计算时间',
      dataIndex: 'calculatedAt',
      key: 'calculatedAt',
      width: 150,
      render: (date: string) => date ? date.substring(0, 16).replace('T', ' ') : '-',
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      fixed: 'right' as const,
      render: (_, record: KpiFact) => (
        <Space>
          <Tooltip title="查看详情">
            <Button type="text" icon={<EyeOutlined />} />
          </Tooltip>
        </Space>
      ),
    },
  ];

  // 计算汇总统计
  const summaryStats = {
    totalKpis: facts.length,
    achievedKpis: facts.filter(f => f.achievementRate >= 100).length,
    improvedKpis: facts.filter(f => f.improvementRate > 0).length,
    avgAchievementRate: facts.length > 0 
      ? Math.round(facts.reduce((sum, f) => sum + f.achievementRate, 0) / facts.length)
      : 0,
  };

  return (
    <div className="space-y-6">
      {/* 统计概览 */}
      <Row gutter={16}>
        <Col span={6}>
          <Card>
            <Statistic
              title="KPI总数"
              value={summaryStats.totalKpis}
              suffix="个"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="达标KPI"
              value={summaryStats.achievedKpis}
              suffix={`/ ${summaryStats.totalKpis}`}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="改善KPI"
              value={summaryStats.improvedKpis}
              suffix="个"
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="平均达成率"
              value={summaryStats.avgAchievementRate}
              suffix="%"
              valueStyle={{ 
                color: summaryStats.avgAchievementRate >= 100 ? '#52c41a' : '#faad14' 
              }}
            />
          </Card>
        </Col>
      </Row>

      {/* KPI数据表格 */}
      <Card>
        <div className="table-toolbar">
          <div className="table-toolbar-left">
            <Input.Search
              placeholder="搜索KPI名称"
              allowClear
              style={{ width: 300 }}
              enterButton={<SearchOutlined />}
            />
            <Select
              placeholder="选择期间"
              style={{ width: 120 }}
              value={selectedPeriod}
              onChange={setSelectedPeriod}
            >
              <Option value="2024-12">2024-12</Option>
              <Option value="2024-11">2024-11</Option>
              <Option value="2024-10">2024-10</Option>
              <Option value="2024-Q4">2024-Q4</Option>
              <Option value="2024-Q3">2024-Q3</Option>
            </Select>
            <Select
              placeholder="选择项目"
              style={{ width: 200 }}
              value={selectedProject}
              onChange={setSelectedProject}
            >
              <Option value="all">全部项目</Option>
              <Option value="proj_001">智能制造系统集成项目</Option>
              <Option value="proj_002">数字化转型咨询项目</Option>
              <Option value="proj_003">云平台建设项目</Option>
            </Select>
          </div>
          <div className="table-toolbar-right">
            <Space>
              <Button icon={<ReloadOutlined />}>
                刷新
              </Button>
              <Button icon={<ExportOutlined />}>
                导出
              </Button>
              <Button
                type="primary"
                icon={<CalculatorOutlined />}
                loading={calculating}
                onClick={handleCalculateKpi}
              >
                重新计算
              </Button>
            </Space>
          </div>
        </div>

        <Table
          columns={columns}
          dataSource={facts}
          rowKey="id"
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
          scroll={{ x: 1400 }}
        />
      </Card>
    </div>
  );
};

export default KpiFacts;
