import React, { useEffect, useState } from 'react';
import {
  Card,
  Typography,
  Row,
  Col,
  Button,
  Select,
  DatePicker,
  Space,
  Table,
  Tag,
  Statistic,
  Tabs,
  List,
  Avatar,
  Progress,
  Tooltip,
} from 'antd';
import {
  DownloadOutlined,
  FileExcelOutlined,
  FilePdfOutlined,
  Bar<PERSON>hartOutlined,
  Line<PERSON>hartOutlined,
  Pie<PERSON><PERSON>Outlined,
  TableOutlined,
  EyeOutlined,
  ShareAltOutlined,
} from '@ant-design/icons';
import { useAppDispatch } from '@store/index';
import { setPageInfo } from '@store/slices/uiSlice';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { TabPane } = Tabs;

interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  type: 'table' | 'chart' | 'dashboard';
  icon: React.ReactNode;
  lastGenerated?: string;
  downloadCount: number;
  size?: string;
}

const Reports: React.FC = () => {
  const dispatch = useAppDispatch();
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);

  useEffect(() => {
    dispatch(setPageInfo({
      title: '报表中心',
      description: '查看和导出各类业务报表'
    }));
  }, [dispatch]);

  // 报表模板数据
  const reportTemplates: ReportTemplate[] = [
    {
      id: 'project_summary',
      name: '项目总览报表',
      description: '项目基本信息、进度、成本、收益等综合报表',
      category: 'project',
      type: 'table',
      icon: <TableOutlined />,
      lastGenerated: '2025-01-14 09:30:00',
      downloadCount: 156,
      size: '2.3MB',
    },
    {
      id: 'kpi_analysis',
      name: 'KPI分析报表',
      description: '各项KPI指标的趋势分析和对比报表',
      category: 'kpi',
      type: 'chart',
      icon: <LineChartOutlined />,
      lastGenerated: '2025-01-14 08:45:00',
      downloadCount: 89,
      size: '1.8MB',
    },
    {
      id: 'financial_report',
      name: '财务分析报表',
      description: '项目成本、收入、毛利率等财务指标报表',
      category: 'financial',
      type: 'table',
      icon: <BarChartOutlined />,
      lastGenerated: '2025-01-13 16:20:00',
      downloadCount: 234,
      size: '3.1MB',
    },
    {
      id: 'settlement_report',
      name: '结算明细报表',
      description: '项目结算详情、成果费计算明细报表',
      category: 'settlement',
      type: 'table',
      icon: <TableOutlined />,
      lastGenerated: '2025-01-13 14:15:00',
      downloadCount: 67,
      size: '1.5MB',
    },
    {
      id: 'customer_analysis',
      name: '客户分析报表',
      description: '客户满意度、合作项目、收益贡献分析',
      category: 'customer',
      type: 'chart',
      icon: <PieChartOutlined />,
      lastGenerated: '2025-01-12 11:30:00',
      downloadCount: 45,
      size: '2.0MB',
    },
    {
      id: 'risk_assessment',
      name: '风险评估报表',
      description: '项目风险识别、评估、应对措施报表',
      category: 'risk',
      type: 'dashboard',
      icon: <BarChartOutlined />,
      lastGenerated: '2025-01-12 10:00:00',
      downloadCount: 78,
      size: '2.7MB',
    },
  ];

  // 最近生成的报表
  const recentReports = [
    {
      name: '2024年度项目总结报表',
      type: 'Excel',
      size: '4.2MB',
      generatedAt: '2025-01-14 09:30:00',
      generatedBy: '张项目经理',
    },
    {
      name: '12月KPI分析报表',
      type: 'PDF',
      size: '1.8MB',
      generatedAt: '2025-01-13 16:45:00',
      generatedBy: '李财务总监',
    },
    {
      name: '客户满意度调研报告',
      type: 'Excel',
      size: '2.1MB',
      generatedAt: '2025-01-13 14:20:00',
      generatedBy: '王销售经理',
    },
  ];

  const handleGenerateReport = (template: ReportTemplate) => {
    console.log('生成报表:', template.name);
    // 这里会调用实际的报表生成API
  };

  const handleDownloadReport = (template: ReportTemplate, format: 'excel' | 'pdf') => {
    console.log('下载报表:', template.name, format);
    // 这里会调用实际的下载API
  };

  const getCategoryText = (category: string) => {
    const categoryTexts = {
      project: '项目管理',
      kpi: 'KPI分析',
      financial: '财务分析',
      settlement: '结算管理',
      customer: '客户分析',
      risk: '风险管理',
    };
    return categoryTexts[category as keyof typeof categoryTexts] || category;
  };

  const getCategoryColor = (category: string) => {
    const categoryColors = {
      project: 'blue',
      kpi: 'green',
      financial: 'orange',
      settlement: 'purple',
      customer: 'cyan',
      risk: 'red',
    };
    return categoryColors[category as keyof typeof categoryColors] || 'default';
  };

  const filteredTemplates = selectedCategory === 'all'
    ? reportTemplates
    : reportTemplates.filter(t => t.category === selectedCategory);

  return (
    <div className="space-y-6">
      {/* 统计概览 */}
      <Row gutter={16}>
        <Col span={6}>
          <Card>
            <Statistic
              title="报表模板"
              value={reportTemplates.length}
              suffix="个"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="本月生成"
              value={89}
              suffix="次"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="本月下载"
              value={234}
              suffix="次"
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="存储空间"
              value={15.6}
              suffix="GB"
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      <Tabs defaultActiveKey="templates">
        <TabPane tab="报表模板" key="templates">
          <Card>
            <div className="flex items-center justify-between mb-4">
              <Space>
                <Select
                  placeholder="选择分类"
                  style={{ width: 150 }}
                  value={selectedCategory}
                  onChange={setSelectedCategory}
                >
                  <Option value="all">全部分类</Option>
                  <Option value="project">项目管理</Option>
                  <Option value="kpi">KPI分析</Option>
                  <Option value="financial">财务分析</Option>
                  <Option value="settlement">结算管理</Option>
                  <Option value="customer">客户分析</Option>
                  <Option value="risk">风险管理</Option>
                </Select>
                <RangePicker
                  placeholder={['开始日期', '结束日期']}
                  value={dateRange}
                  onChange={setDateRange}
                />
              </Space>
              <Button type="primary" icon={<DownloadOutlined />}>
                批量导出
              </Button>
            </div>

            <Row gutter={[16, 16]}>
              {filteredTemplates.map((template) => (
                <Col span={8} key={template.id}>
                  <Card
                    hoverable
                    actions={[
                      <Tooltip title="预览">
                        <EyeOutlined onClick={() => console.log('预览', template.name)} />
                      </Tooltip>,
                      <Tooltip title="下载Excel">
                        <FileExcelOutlined
                          onClick={() => handleDownloadReport(template, 'excel')}
                        />
                      </Tooltip>,
                      <Tooltip title="下载PDF">
                        <FilePdfOutlined
                          onClick={() => handleDownloadReport(template, 'pdf')}
                        />
                      </Tooltip>,
                      <Tooltip title="分享">
                        <ShareAltOutlined onClick={() => console.log('分享', template.name)} />
                      </Tooltip>,
                    ]}
                  >
                    <Card.Meta
                      avatar={
                        <Avatar
                          size={48}
                          icon={template.icon}
                          style={{ backgroundColor: '#1890ff' }}
                        />
                      }
                      title={
                        <div>
                          <div className="flex items-center justify-between">
                            <Text strong>{template.name}</Text>
                            <Tag color={getCategoryColor(template.category)}>
                              {getCategoryText(template.category)}
                            </Tag>
                          </div>
                        </div>
                      }
                      description={
                        <div>
                          <Text type="secondary" className="text-sm">
                            {template.description}
                          </Text>
                          <div className="mt-2 flex justify-between text-xs text-gray-500">
                            <span>下载 {template.downloadCount} 次</span>
                            {template.size && <span>{template.size}</span>}
                          </div>
                          {template.lastGenerated && (
                            <div className="mt-1 text-xs text-gray-400">
                              最后生成: {template.lastGenerated.substring(5, 16).replace('T', ' ')}
                            </div>
                          )}
                        </div>
                      }
                    />
                    <div className="mt-3">
                      <Button
                        type="primary"
                        block
                        onClick={() => handleGenerateReport(template)}
                      >
                        生成报表
                      </Button>
                    </div>
                  </Card>
                </Col>
              ))}
            </Row>
          </Card>
        </TabPane>

        <TabPane tab="最近生成" key="recent">
          <Card>
            <List
              itemLayout="horizontal"
              dataSource={recentReports}
              renderItem={(item) => (
                <List.Item
                  actions={[
                    <Button type="link" icon={<DownloadOutlined />}>
                      下载
                    </Button>,
                    <Button type="link" icon={<ShareAltOutlined />}>
                      分享
                    </Button>,
                  ]}
                >
                  <List.Item.Meta
                    avatar={
                      <Avatar
                        icon={item.type === 'Excel' ? <FileExcelOutlined /> : <FilePdfOutlined />}
                        style={{
                          backgroundColor: item.type === 'Excel' ? '#52c41a' : '#ff4d4f'
                        }}
                      />
                    }
                    title={item.name}
                    description={
                      <div>
                        <div>生成时间: {item.generatedAt}</div>
                        <div>生成人: {item.generatedBy} · 文件大小: {item.size}</div>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </TabPane>

        <TabPane tab="使用统计" key="statistics">
          <Row gutter={16}>
            <Col span={12}>
              <Card title="热门报表排行">
                <List
                  size="small"
                  dataSource={reportTemplates
                    .sort((a, b) => b.downloadCount - a.downloadCount)
                    .slice(0, 5)
                  }
                  renderItem={(item, index) => (
                    <List.Item>
                      <div className="flex items-center w-full">
                        <div className="w-8 text-center">
                          <Text strong style={{
                            color: index < 3 ? '#faad14' : '#8c8c8c'
                          }}>
                            {index + 1}
                          </Text>
                        </div>
                        <div className="flex-1 ml-3">
                          <div>{item.name}</div>
                          <Progress
                            percent={Math.round((item.downloadCount / 234) * 100)}
                            size="small"
                            showInfo={false}
                          />
                        </div>
                        <div className="text-right">
                          <Text strong>{item.downloadCount}</Text>
                          <div className="text-xs text-gray-500">次下载</div>
                        </div>
                      </div>
                    </List.Item>
                  )}
                />
              </Card>
            </Col>
            <Col span={12}>
              <Card title="分类使用情况">
                <div className="space-y-4">
                  {['project', 'financial', 'kpi', 'settlement', 'customer', 'risk'].map(category => {
                    const categoryReports = reportTemplates.filter(t => t.category === category);
                    const totalDownloads = categoryReports.reduce((sum, r) => sum + r.downloadCount, 0);
                    const maxDownloads = Math.max(...reportTemplates.map(r => r.downloadCount));

                    return (
                      <div key={category}>
                        <div className="flex justify-between mb-1">
                          <Text>{getCategoryText(category)}</Text>
                          <Text strong>{totalDownloads}次</Text>
                        </div>
                        <Progress
                          percent={Math.round((totalDownloads / (maxDownloads * 2)) * 100)}
                          strokeColor={getCategoryColor(category)}
                        />
                      </div>
                    );
                  })}
                </div>
              </Card>
            </Col>
          </Row>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default Reports;
